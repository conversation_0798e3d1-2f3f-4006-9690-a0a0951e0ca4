import { useEffect } from 'react'
import { trpcReact } from '@/trpc'
import { fillCart, setCartIsLoading } from '@/stores/cart'
import { TrpcReactWrapper } from './TrcpReactWrapper'

// Компонент для загрузки данных корзины
const CartLoaderInner = () => {
  const { data, isLoading, isError, isFetching } = trpcReact.products.getCartItems.useQuery(undefined, {
    refetchOnMount: true,
    refetchOnWindowFocus: true,
    retry: 2
  })

  useEffect(() => {
    setCartIsLoading(isFetching)
  }, [isFetching])

  useEffect(() => {
    if (data) {
      fillCart(data)
    }
  }, [data])

  if (isError) {
    console.warn('Ошибка загрузки корзины')
  }

  return null // Компонент не рендерит ничего
}

// Обертка с TrpcReactWrapper для корректной работы trpc
export const CartLoader = () => {
  return (
    <TrpcReactWrapper>
      <CartLoaderInner />
    </TrpcReactWrapper>
  )
}
