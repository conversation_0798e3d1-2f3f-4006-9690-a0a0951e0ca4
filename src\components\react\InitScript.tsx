import { useEffect } from 'react'
import { trpc } from '@/trpc'
import type { AppSettings } from '@/types/AppSettings'
import { fillSettings } from '@/stores/settings'
import { clearUser, setUser, type Client, userStore } from '@/stores/user'

/**
 * Компонент для инициализации данных и настроек приложения
 * Заменяет скрипт из Layout.astro для лучшей совместимости между браузерами
 */
export const InitScript = () => {
  useEffect(() => {
    init()
  }, [])

  /**
   * Получает значение куки по имени
   */
  function getCookie(name: string) {
    let matches = document.cookie.match(new RegExp('(?:^|; )' + name.replace(/([\.$?*|{}\(\)\[\]\\\/\+^])/g, '\\$1') + '=([^;]*)'))
    return matches ? decodeURIComponent(matches[1]) : undefined
  }

  /**
   * Загружает данные пользователя
   */
  async function loadUser() {
    if (userStore.value?.client_id) {
      return userStore.value
    }
    try {
      const response = await fetch('/api/auth/login/check', {
        credentials: 'include'
      })

      if (response.ok) {
        const data: Client = await response.json()

        setUser({
          ...data
        })

        return data
      } else {
        clearUser()
        return null
      }
    } catch (error) {
      // console.error('Ошибка при проверке авторизации:', error);
      clearUser()

      return null
    }
  }

  /**
   * Загружает настройки приложения
   */
  async function loadSettings() {
    const settings: AppSettings = await trpc.services.getSettings.query({
      params: ['free_shipping.intro_text','free_shipping.startsum','free_shipping.isactive','seo_templates','copyaccess_users', 'pochta.express.maxValue', 'pochta.standard.maxValue', 'shipping.kce.active', 'shipping.ems.active', 'shipping.cdek.active']
    })

    if (settings) {
      fillSettings(settings)
      return settings
    }

    return null
  }

  /**
   * Восстанавливает стандартное поведение контекстного меню и выделения текста
   */
  function restorectxm() {
    function removeEventListeners() {
      const events = ['contextmenu', 'copy', 'cut']
      try {
        for (const event of events) {
          document.removeEventListener(event, preventDefaultHandler, true)
        }
      } catch (error) {}
    }

    function restoreOnSelectStart() {
      document.onselectstart = null
    }

    function preventDefaultHandler(event: Event) {
      event.preventDefault()
    }

    removeEventListeners()
    restoreOnSelectStart()
  }

  /**
   * Инициализирует приложение
   */
  async function init() {
    try {
      const user = await loadUser()
      const settings = await loadSettings()

      const excludePages = ['/', '/contacts', '/cart']

      try {
        if (excludePages.includes(window.location.pathname)) {
          restorectxm()
          return false
        }
      } catch (error) {}

      try {
        if (user?.client_number && settings?.copyaccess_users?.includes(user.client_number)) {
          restorectxm()
          return false
        }
      } catch (error) {
        // console.log('@', error);
      }

      try {
        // Проверяем, есть ли кука 'ctoken'
        if (!getCookie('ctoken')) {
          // // Блокируем контекстное меню (правую кнопку мыши)
          // document.oncontextmenu = () => false

          // try {
          //   // Блокируем выделение текста
          //   document.addEventListener('selectstart', (event) => {
          //     if (!(event.target as HTMLElement).matches('input, textarea')) {
          //       event.preventDefault()
          //     }
          //   })

          //   // Блокируем копирование
          //   document.addEventListener('copy', (event) => {
          //     if (!(event.target as HTMLElement).matches('input, textarea')) {
          //       event.preventDefault()
          //     }
          //   })

          //   // Блокируем вырезание
          //   document.addEventListener('cut', (event) => {
          //     if (!(event.target as HTMLElement).matches('input, textarea')) {
          //       event.preventDefault()
          //     }
          //   })
          // } catch (error) {}
        }
      } catch (error) {
        // console.warn('Ошибка при блокировке действий', error);
      }
    } catch (error) {
      console.warn('init error: ', error)
    }
  }

  // Компонент не рендерит никакой UI
  return null
}

export default InitScript
