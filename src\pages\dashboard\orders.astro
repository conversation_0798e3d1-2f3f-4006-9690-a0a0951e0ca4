---
import Layout from '@layouts/Layout.astro'
import { UserOrders } from '@components/react/users/Orders'
import { useSSRtRPC } from '@/trpc'
import { BackHistoryButton } from '@components/react/BackHistoryButton'

// Получаем cookie header из запроса
const cookieHeader = Astro.request.headers.get('cookie') || ''

// Передаем объект с cookieHeader
const trpc = useSSRtRPC({ cookieHeader })

const page = Astro.url.searchParams.get('page') || 1
const data = await trpc.orders.getUserOrders.query({
  page: Number(page)
})

const isMobile = Astro.locals.isMobile
// console.log("🚀 ~ data:", data)
---

<Layout title='Мои заказы'>
  <div>
    <BackHistoryButton goto='/' variant='light' size='sm' label='Назад' client:idle />
  </div>
  <div class='mx-auto p-3 xl:container lg:p-5'>
    <UserOrders isMobile={isMobile} page={page} client:load data={data} />
  </div>
</Layout>
