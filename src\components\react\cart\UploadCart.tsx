import React, { useRef, useState } from 'react'
import { Input, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from '@heroui/react'
import { FileUploader } from '../FileUploader'

interface UploadCartProps {
  onSuccess?: () => void
}

export const UploadCart: React.FC<UploadCartProps> = ({ onSuccess }) => {
  const formRef = useRef<HTMLFormElement>(null)
  const [file, setFile] = useState<File | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const handleFileUpload = (files: File[]) => {
    setFile(files[0] || null)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError(null)
    try {
      if (!formRef.current || !file) {
        setError('Выберите файл')
        setLoading(false)
        return
      }
      const formData = new FormData(formRef.current)
      formData.set('file', file)
      const response = await fetch('/api/service/parsexlsfile/', {
        method: 'POST',
        body: formData
      })

      if (!response.ok) {
        const m = await response.json()
        throw new Error(m?.message || 'Ошибка загрузки файла')
      }
      if (onSuccess) onSuccess()
    } catch (err: any) {
      setError(err.message || 'Ошибка')
    } finally {
      setLoading(false)
    }
  }

  return (
    <form ref={formRef} onSubmit={handleSubmit} className='mt-4 w-full space-y-4 rounded-md border p-4 dark:border-default-200'>
      <h1 className='mb-2 text-center text-base font-bold'>Загрузка файла корзины</h1>
      <div className='flex flex-wrap items-center justify-center gap-4'>
        <div className='w-full md:w-1/4'>
          <Input type='number' name='sku_column' label='Номер столбца с артикулом' defaultValue='1' min={1} className='w-full' />
        </div>
        <div className='w-full md:w-1/4'>
          <Input type='number' name='qty_column' label='Номер столбца с кол-вом' defaultValue='2' min={1} className='w-full' />
        </div>
        <div className='w-full md:w-1/4'>
          <Input type='number' name='brand_column' label='Номер столбца с брендом' defaultValue='3' min={1} className='w-full' />
        </div>
        <div className='my-4 flex w-full flex-col items-center md:w-1/4'>
          <FileUploader
            onUpload={handleFileUpload}
            accept='.xlsx,.csv,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel,text/csv'
            multiple={false}
            maxSize={10485760}
          />
        </div>
      </div>
      <div className='flex w-full flex-col items-center space-y-2'>
        <div>
          <Alert color='danger'>Текущее содержимое корзины будет заменено</Alert>
        </div>
        {error && <Alert color='danger'>{error}</Alert>}
      </div>
      <div className='flex justify-end'>
        <Button color='warning' variant='flat' isLoading={loading} type='submit' disabled={loading}>
          <span>Загрузить</span>
        </Button>
      </div>
    </form>
  )
}

export default UploadCart
