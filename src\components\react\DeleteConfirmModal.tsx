import { memo } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>dalBody, ModalFooter } from '@heroui/react'
import { Button } from '@heroui/react'

interface DeleteConfirmModalProps {
  isOpen: boolean
  onClose: () => void
  onConfirm: () => void
}

export const DeleteConfirmModal = memo(({ isOpen, onClose, onConfirm }: DeleteConfirmModalProps) => {
  //console.log('confirm remove modal rendering')

  return (
    <Modal isOpen={isOpen} onClose={onClose} size='sm'>
      <ModalContent>
        <ModalHeader>Подтверждение</ModalHeader>
        <ModalBody>Удалить товар из корзины?</ModalBody>
        <ModalFooter>
          <Button color='danger' variant='flat' onPress={onConfirm}>
            Удалить
          </Button>
          <Button color='default' variant='flat' onPress={onClose}>
            Отмена
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  )
})
