import { atom } from 'nanostores'

// Интерфейс для состояния редактора товаров
interface ProductEditorState {
  isOpen: boolean
  productId: string | null
}

// Создаем атом с начальным состоянием
export const $productEditor = atom<ProductEditorState>({
  isOpen: false,
  productId: null
})

// Функция для открытия модального окна редактора
export function openProductEditor(productId: string) {
  $productEditor.set({
    isOpen: true,
    productId
  })
}

// Функция для закрытия модального окна редактора
export function closeProductEditor() {
  $productEditor.set({
    isOpen: false,
    productId: null
  })
}
