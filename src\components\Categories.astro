---
import { trpc } from '@/trpc'
import { Image } from 'astro:assets'

interface FallbackCategoryType {
  cat_id: number
  cat_title: string
  cat_pic: string
  cat_url: string
  cat_note: string | null
  subcategories?: FallbackCategoryType[]
}

type CategoriesDataType = Awaited<ReturnType<typeof trpc.products.getRootCategories.query>>

interface Props {
  categories: CategoriesDataType | null
}

// const categories = await trpc.products.getRootCategories.query()

const { categories } = Astro.props
---

<section class='container mx-auto px-4 py-5 sm:py-20'>
  <div class='grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3'>
    {
      categories?.map((category) => (
        <a
          href={
            category.cat_rootcat !== 0
              ? `${Astro.url.pathname}/${category.cat_url || category.cat_url_ru}`
              : `/catalog/${category.cat_url || category.cat_url_ru}`
          }
          class='group relative border-b-2 shadow-lg border-default-600 overflow-hidden rounded-md p-6 backdrop-blur-sm transition-all duration-500 hover:shadow-[0_8px_30px_rgb(0,0,0,0.12)] dark:bg-default-50'
        >
          {/* <div class='absolute inset-0 bg-gradient-to-r from-violet-500/10 via-fuchsia-500/10 to-cyan-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500' /> */}

          <div class='relative z-10 h-12 sm:h-24'>
            <div class='mb-8 flex items-end justify-between'>
              <h2 class={`bg-clip-text text-lg md:text-xl lg:text-2xl font-bold leading-none mb-3 sm:mb-0`}>{category.cat_title}</h2>

              <div class='relative flex h-10 w-10 items-center justify-center overflow-hidden rounded-full bg-default-200 transition-colors duration-500 dark:bg-default-100'>
                <svg
                  xmlns='http://www.w3.org/2000/svg'
                  class='h-5 w-5 transform transition-transform duration-500 group-hover:translate-x-8'
                  fill='none'
                  viewBox='0 0 24 24'
                  stroke='currentColor'
                >
                  <path stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M14 5l7 7m0 0l-7 7m7-7H3' />
                </svg>
                <svg
                  xmlns='http://www.w3.org/2000/svg'
                  class='absolute h-5 w-5 -translate-x-8 transform transition-transform duration-500 group-hover:translate-x-0'
                  fill='none'
                  viewBox='0 0 24 24'
                  stroke='currentColor'
                >
                  <path stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M14 5l7 7m0 0l-7 7m7-7H3' />
                </svg>
              </div>
            </div>
          </div>
          <div class={`${category.cat_rootcat == 0 ? 'flex justify-between' : 'flex justify-center'}`}>
            <div class='prose prose-lg text-default-800 max-w-40 sm:max-w-60 text-ellipsis text-sm sm:text-base transition-colors duration-500 group-hover:text-default-700' set:html={category.cat_note} />

            <div class={`relative flex-shrink-0 ${category.cat_rootcat == 0 ? 'h-36 w-36' : ''}`}>
              {/* <div class='absolute inset-0 bg-gradient-to-br from-violet-500/20 to-cyan-500/20 rounded-2xl opacity-0 group-hover:opacity-100 transition-all duration-500 blur-xl' /> */}
              <Image
                src={category.cat_pic}
                alt={category.cat_title}
                width={isNaN(parseInt(category.imagesize)) ? 180 : parseInt(category.imagesize)}
                height={isNaN(parseInt(category.imagesize)) ? 180 : parseInt(category.imagesize)}
                class={`${category.cat_rootcat == 0 ? 'absolute bottom-0 right-0 -mb-6 -mr-6 object-contain' : ''} z-10`}
              />
            </div>
          </div>
        </a>
      ))
    }
  </div>
</section>

<style></style>
