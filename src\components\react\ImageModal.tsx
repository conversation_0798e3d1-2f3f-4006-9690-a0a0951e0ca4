import { memo } from 'react'
import { Mo<PERSON>, ModalContent, ModalBody, Button } from '@heroui/react'

interface ImageModalProps {
  isOpen: boolean
  onOpenChange: (open: boolean) => void
  selectedImage: string
}

const ImageModal = memo(({ isOpen, onOpenChange, selectedImage }: ImageModalProps) => {
  return (
    <Modal
      size='5xl'
      isOpen={isOpen}
      onOpenChange={onOpenChange}
      closeButton={
        <Button isIconOnly color='danger' variant='flat'>
          <span className='text-xl font-bold text-danger'>x</span>
        </Button>
      }
      motionProps={{
        variants: {
          enter: {
            y: 0,
            opacity: 1,
            transition: {
              duration: 0.3,
              ease: 'easeOut'
            }
          },
          exit: {
            y: -20,
            opacity: 0,
            transition: {
              duration: 0.2,
              ease: 'easeIn'
            }
          }
        }
      }}
    >
      <ModalContent>
        {(onClose) => (
          <ModalBody className='flex h-[80vh] items-center justify-center bg-default-50 p-8'>
            <img
              src={selectedImage}
              alt='Selected product image'
              className='max-h-[calc(80vh-4rem)] w-auto max-w-full object-contain dark:brightness-75'
            />
          </ModalBody>
        )}
      </ModalContent>
    </Modal>
  )
})

ImageModal.displayName = 'ImageModal'

export default ImageModal
