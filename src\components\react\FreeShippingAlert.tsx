import { Alert } from '@heroui/react'
import { trpc, trpcReact } from '@/trpc'
import { TrpcReactWrapper } from './TrcpReactWrapper'


const AContent = () => {

  const { data, isLoading, isSuccess } = trpcReact.services.getSettings.useQuery({
    params: ['free_shipping.intro_text', 'free_shipping.startsum', 'free_shipping.isactive']
  }, {
    enabled: !window.localStorage.getItem('readShippingAlert')
  })
  
  const closeAlertHandler = () => {
    window.localStorage.setItem('readShippingAlert', '1')
  }
  

  return (
    <div className='mx-auto md:container'>
      {isSuccess && !!data?.['free_shipping.isactive'] && (
        <div className='w-full px-4 py-2'>
          <Alert onClose={closeAlertHandler} isClosable color='success' variant='faded' title='Акция!' description={data?.['free_shipping.intro_text']}  />
        </div>
      )}
    </div>
  )
}

export const FreeShippingAlert = () => {
  return (
    <TrpcReactWrapper>
      <AContent />
    </TrpcReactWrapper>
  )
}
