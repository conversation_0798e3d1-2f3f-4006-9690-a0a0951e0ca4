---
import { getPageParams } from '@/lib/pageUtils'
import ProductViewSwitcher from '@components/ProductViewSwitcher.astro'
import { CartProducts } from '@components/react/cart/CartProducts'
import { Checkout } from '@components/react/cart/Checkout'
import { Skeleton } from '@heroui/react'
import Layout from '@layouts/Layout.astro'
import { CartSorting } from '@components/react/cart/CartSorting'

const searchParams = Astro.url.searchParams

const { limit, page, order, sorting, filters } = getPageParams(searchParams)

const isMobile = Astro.locals.isMobile
const currentViewMode = Astro.locals.viewMode

// Добавляем заголовки безопасности
Astro.response.headers.set('X-Robots-Tag', 'noindex, nofollow')
Astro.response.headers.set('Cache-Control', 'private, no-cache, no-store, must-revalidate')
Astro.response.headers.set('Pragma', 'no-cache')
Astro.response.headers.set('Expires', '0')

// 17376 - 923575 - <EMAIL>
---

<Layout description='' title='Корзина'>
  <meta name='robots' content='noindex, nofollow' />
  <meta name='googlebot' content='noindex, nofollow' />
  <meta name='yandex' content='none' />

  <div class='p2 w-full xl:container md:p-5 xl:mx-auto'>
    <div class='mb-4 flex items-center justify-end gap-4'>
      <div>
        {!isMobile && <CartSorting isMobile={isMobile} client:only='react' initialSorting={sorting?.[0]?.column} />}
      </div>
      {
        !isMobile && (
          <div>
            <ProductViewSwitcher server:defer />
          </div>
        )
      }
    </div>

    <CartProducts isMobile={isMobile} viewMode={currentViewMode} client:only='react' page={page} sorting={sorting}>
      {[...Array(5)].map((_, i) => <Skeleton key={i} className='h-20 w-full rounded-lg' />)}
    </CartProducts>

    <div class='flex flex-col gap-3'>
      <div>
        <Checkout client:only='react'>
          <div slot='fallback'>
            <Skeleton className='h-40 w-full rounded-lg' />
          </div>
        </Checkout>
      </div>
    </div>
  </div>
</Layout>
