import { <PERSON>er, <PERSON>er<PERSON>ontent, <PERSON>er<PERSON>eader, <PERSON><PERSON><PERSON><PERSON>, <PERSON>er<PERSON>ooter, But<PERSON>, useDisclosure } from '@heroui/react'
import { LoginWidget } from '@components/react/users/LoginWidget'
import type { ReactNode } from 'react'
import { UserIcon } from 'lucide-react'

interface Props {
    activator?: (onOpen: () => void) => ReactNode
}

export const LoginDrawe = ({ activator }: Props) => {
    const { isOpen, onOpen, onOpenChange } = useDisclosure()

    return (
        <>
            {activator ? (
                <span>{activator(onOpen)}</span>
            ) : (
                <Button size='lg' color='default' onPress={onOpen}>
                    <span className='hidden lg:inline'>Войти</span> <UserIcon />
                </Button>
            )}
            <Drawer backdrop='opaque' isOpen={isOpen} onOpenChange={onOpenChange}>
                <DrawerContent>
                    {(onClose) => (
                        <>
                            <DrawerHeader className='flex flex-col gap-1'>Авторизация</DrawerHeader>
                            <DrawerBody>
                                <LoginWidget onToReg={() => onClose()} />
                            </DrawerBody>
                            <DrawerFooter>
                                <Button color='danger' variant='light' onPress={onClose}>
                                    Закрыть
                                </Button>
                            </DrawerFooter>
                        </>
                    )}
                </DrawerContent>
            </Drawer>
        </>
    )
}
