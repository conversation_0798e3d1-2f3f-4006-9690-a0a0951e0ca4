import { useState } from 'react'
import { Breadcrumbs, BreadcrumbItem } from '@heroui/react'

type BcItem = {
  key: string
  label: string
}
interface Props {
  path?: string
  prefix?: string
  items?: BcItem[]
}

const defaultKeys = {
  'salniki-rulevyh-reek': 'Сальники рулевых реек',
  'komplektuyushchie-dlya-remonta-rulevyh-reek': 'Комплектующие для р/реек',
  'pylniki-rulevyh-reek': 'Пыльники рулевых реек',
  'salniki-razlichnogo-primeneniya': 'Сальники различного применения',
  'remkomplekty-rulevyh-reek': 'Ремкомплекты рулевых реек',
  'rezinovye-kolca-kruglogo-secheniya': 'Резиновые кольца круглого сечения',
  'teflonovye-kolca': 'Тефлоновые кольца',
  'vtulki': 'Втулки',
  'gajki': 'Гайки',
  'podzhimy': 'Поджимы',
  'podshipniki_rulevyh_reek': 'Подшипники рулевых реек',
  'homuty': 'Хомуты',
  'bez-kategorii': 'Без категории',
  'porshni': 'Поршни',
  'krestoviny': 'Крестовины',
  'remkomplekty-gidrocilindrov': 'Ремкомплекты гидроцилиндров',
  'remni-rulevih-reek': 'Ремни рулевых реек',
  'remkomplekty-rulevyh-reduktorov': 'Ремкомплекты рулевых редукторов',
  'salniki-gidromotory-gidronasosy': 'Сальники гидромоторы, гидронасосы',
  'komplektuyushchie-dlya-gidrocilindrov': 'Комплектующие гидроцилиндров',
  'salniki': 'Сальники',
  'remkomplekty': 'Ремкомплекты',
  'podshipniki_razlichnogo_primeneniya': 'Подшипники различного применения',
  'podshipniki': 'Подшипники',
  'remni': 'Ремни',
  'remni-moto-cvt': 'Ремни мототехники (CVT)',
  'salniki-mototekhniki': 'Сальники мототехники',
  'shtokovye-uplotneniya-hydrocilindrov': 'Штоковые уплотнения',
  'porshnevye-uplotneniya-hydrocilindrov': 'Поршневые уплотнения',
  'rezinovye-kolca-hydrocilindrov': 'Резиновые кольца гидроцилиндров',
  'oporno-napravlyayushchie-kolca': 'Опорно-направляющие кольца',
  'gryazesemniki-gidrocilindrov': 'Грязесъемники',
  'manzhety-shtoka-i-porshnya-gidrocilindrov': 'Манжеты штока и поршня',
  'drugie-uplotneniya': 'Другие уплотнения',
  'salniki-motor-reduktorov': 'Сальники мотор-редукторов',
  'salniki-specialnogo-naznachenia': 'Сальники специального назначения',
  'zaglushki': 'Заглушки',
  'remni-agro': 'Ремни агротехники',
  'salniki-oem': 'Сальники OEM',
  'remkomplekty-mototekhniki': 'Ремкомплекты мототехники',
  'rezinovye-kolca': 'Резиновые кольца',
  'rezinovye-kolca-pryamougolnogo-secheniya': 'Резиновые кольца прямоугольного сечения',
  'rezinovye-kolca-x-obraznye': 'Резиновые кольца Х-образного сечения',
  'rezinovye-kolca-v-obraznye': 'Резиновые кольца V-образного сечения',
  'catalog': 'Главная'
}

export default function BreadcrumbsNav({ path = '', prefix = '/catalog/', items = [] }: Props) {
  // Normalize slashes by replacing multiple slashes with single slash
  const normalizedPath = path.replace(/\/+/g, '/')
  const normalizedPrefix = prefix.replace(/\/+/g, '/')

  const navItems = [
    ...items,
    ...String(normalizedPath)
      ?.split?.('/')
      .filter((i) => i)
      .map((i) => ({ key: i, label: i }))
  ].filter((i) => i.key)

  const [currentPage, setCurrentPage] = useState(navItems[navItems.length - 1]?.key)

  return (
    <Breadcrumbs underline='active' onAction={(key) => setCurrentPage(key)}>
      {navItems.map((item, index) => {
        const href = `/${item.key === 'catalog' ? '' : normalizedPrefix + item.key}`.replace(/\/+/g, '/')
        return (
          <BreadcrumbItem key={index} href={href} isCurrent={currentPage === item.key}>
            {defaultKeys[item.label] || defaultKeys[item.key] || item.label || item.key}
          </BreadcrumbItem>
        )
      })}
    </Breadcrumbs>
  )
}
