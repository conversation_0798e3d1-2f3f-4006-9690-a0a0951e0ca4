// @ts-check
import { defineConfig } from 'astro/config'
import tailwind from '@astrojs/tailwind'
import react from '@astrojs/react'
import node from '@astrojs/node'
import playformCompress from '@playform/compress'
import MillionLint from '@million/lint'

const proxyTarget = import.meta.env.DEV ? import.meta.env.VITE_API_URL_DEV : import.meta.env.VITE_API_URL

export default defineConfig({
  devToolbar: {
    enabled: false
  },
  integrations: [
    tailwind({ applyBaseStyles: false }),
    react(),
    playformCompress(),
    MillionLint.astro({
      optimizeDOM: true,
      telemetry: false,
      react: '19',
      skipTransform: false,
      rsc: false,
      dev: false
    })
  ],
  compressHTML: true,

  build: {
    inlineStylesheets: 'auto'
  },

  vite: {
    plugins: [],
    // Оптимизация зависимостей
    optimizeDeps: {
      include: ['react', 'react-dom', '@heroui/react', '@tanstack/react-table'],
      exclude: ['@playform/compress']
    },
    build: {
      rollupOptions: {
        output: {
          manualChunks: {
            // Основные библиотеки
            vendor: ['react', 'react-dom'],
            ui: ['@heroui/react'],
            // Разделение тяжелых библиотек
            motion: ['framer-motion'],
            tanstack: ['@tanstack/react-table', '@tanstack/react-virtual', '@tanstack/react-query'],
            // Компоненты поиска
            search: ['@/components/react/InstantSearchBox', '@/components/react/SearchBar'],
            // Компоненты таблиц и карточек
            products: ['@/components/react/ProductList', '@/components/react/ProductsCards'],
            // Добавляем новые чанки
            cart: ['@/components/react/cart/CartProducts', '@/components/react/QtyInput', '@/components/react/PreorderButton'],
            utils: ['@/utils', '@/stores'],
            forms: ['@/components/react/forms']
          }
        }
      },
      cssCodeSplit: true,
      modulePreload: true,
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: true,
          passes: 2,
          ecma: 2020,
          module: true
        },
        format: {
          comments: false
        }
      },
      // Дополнительные оптимизации сборки
      assetsInlineLimit: 4096, // 4kb
      sourcemap: false,
      chunkSizeWarningLimit: 1000,
      reportCompressedSize: false,
      cssMinify: 'lightningcss'
    },
    server: {
      proxy: {
        '/trpc': {
          target: proxyTarget,
          changeOrigin: true
        },
        '/api': {
          target: proxyTarget,
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, '')
        }
      }
    },
    // Оптимизация для продакшена
    define: {
      'process.env.NODE_ENV': JSON.stringify('production')
    }
  },

  image: {
    service: {
      entrypoint: 'astro/assets/services/sharp',
      config: {
        formats: ['webp', 'avif'],
        quality: 80,
        densities: [1, 2],
        minimumCacheTTL: 60 * 60 * 24 * 7, // 7 дней
        // Добавляем автоматическое изменение размера
        defaultWidth: 800,
        breakpoints: [320, 640, 768, 1024, 1280]
      }
    }
  },

  output: 'server',
  adapter: node({
    mode: 'standalone'
  }),

  experimental: {
    clientPrerender: true,
    directRenderScript: true,
    hybridOutput: true
  },

  prefetch: {
    prefetchAll: true,
    defaultStrategy: 'viewport'
  }
})
