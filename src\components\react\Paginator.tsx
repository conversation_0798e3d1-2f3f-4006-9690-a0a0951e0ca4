import { Button, Input, Pagination, Select, SelectItem } from '@heroui/react'
import { updateURLParams, currentPageStore } from '@/stores/qs'
import { useState, useEffect, useCallback, useRef } from 'react'

import { ArrowRight, ChevronLeftIcon, ChevronRightIcon, ChevronRightSquareIcon } from 'lucide-react'
import { useStore } from '@nanostores/react'

interface Props {
  initPage?: number
  total: number
  limit?: number
  showSelectLimit?: boolean
  isMobile?: boolean
  showPageInput?: boolean
}

function useDebounce<T extends (...args: any[]) => void>(fn: T, delay: number): T {
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)

  return ((...args: Parameters<T>) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }
    timeoutRef.current = setTimeout(() => fn(...args), delay)
  }) as T
}

export const Paginator = ({ initPage = 1, total = 0, limit = 200, showSelectLimit = false, isMobile = false, showPageInput = false }: Props) => {
  const [limitValue, setLimitValue] = useState(new Set([]))
  const currentPage = useStore(currentPageStore)
  const [inputPageValue, setInputPageValue] = useState<String | undefined>(initPage)

  // Используем useEffect только при первом рендере для установки начальной страницы
  useEffect(() => {
    if (initPage && initPage !== currentPage) {
      currentPageStore.set(initPage) // Используем напрямую стор вместо setCurrentPage
    }
  }, []) // Пустой массив зависимостей

  useEffect(() => {
    setInputPageValue(currentPage)
  }, [currentPage])

  const inputPageValueHandler = (v: string) => {
    let _value = Number(v)

    if (_value == currentPage) {
      _value++
    }

    if (_value < 1) {
      _value = 1
      v = '1'
    } else if (_value > totalPages) {
      _value = totalPages
      v = String(totalPages)
    }
    if (_value > 0 && _value !== currentPage) {
      updateURLParams({ page: _value }, undefined, false)
    }
  }

  const debouncedInputPageValueHandler = useDebounce(inputPageValueHandler, 700)

  const limitItems = [
    { key: '10', label: '10' },
    { key: '20', label: '20' },
    { key: '50', label: '50' },
    { key: '100', label: '100' },
    { key: '200', label: '200' }
  ]

  const totalPages = total

  return (
    total > 0 &&
    (isMobile ? (
      // Мобильная версия
      <div className='flex items-center justify-end gap-2 rounded-lg border bg-white dark:border-default-400 dark:bg-default-50'>
        <Button
          // isIconOnly
          variant='faded'
          size='sm'
          radius='sm'
          onPress={() => {
            if (currentPage > 1) {
              updateURLParams({ page: currentPage - 1 }, undefined, false)
            }
          }}
          isDisabled={currentPage === 1}
        >
          Назад <ChevronLeftIcon className='h-4 w-4' />
        </Button>
        {/* <span>
          стр. {currentPage} из {totalPages}
        </span> */}
        {showPageInput && totalPages > 1 && (
          <div className='flex items-center'>
            <Input
              labelPlacement='outside-left'
              label='стр.'
              variant='bordered'
              size='sm'
              //foramt
              className='mr-1 w-20'
              value={inputPageValue}
              onValueChange={(value) => setInputPageValue(value)}
              max={totalPages}
              min={1}
              onChange={(e) => debouncedInputPageValueHandler(e.target.value)}
            />
            <span>из {totalPages}</span>
          </div>
        )}
        <Button
          // isIconOnly
          variant='faded'
          size='sm'
          radius='sm'
          onPress={() => {
            if (currentPage < totalPages) {
              updateURLParams({ page: currentPage + 1 }, undefined, false)
            }
          }}
          isDisabled={currentPage === totalPages}
        >
          Вперед <ChevronRightIcon className='h-4 w-4' />
        </Button>
      </div>
    ) : (
      <div className='flex items-center gap-2'>
          {showPageInput && totalPages > 1 && (
          <div className='flex items-center rounded-md bg-default-200 p-1'>
            <Input
              labelPlacement='outside-left'
              label='стр.'
              variant='flat'
              size='sm'
              //foramt
              className='mr-1 w-20 rounded-l-md shadow-md'
              value={inputPageValue}
              onValueChange={(value) => setInputPageValue(value)}
              max={totalPages}
              min={1}
              // onChange={(e) => inputPageValueHandler(e.target.value)}
            />
            <Button
              //format
              onPress={() => inputPageValueHandler(inputPageValue)}
              size='sm'
              variant='bordered'
              isIconOnly
            >
              <ArrowRight className='w-5' />
            </Button>
          </div>
        )}

        <Pagination
          isCompact
          showControls
          loop
          size='md'
          boundaries={1}
          radius='sm'
          siblings={1}
          total={total}
          color='default'
          dotsJump={5}
          showShadow
          page={currentPage}
          onChange={(page) => {
            updateURLParams({ page }, undefined, false)
          }}
        />
        {showSelectLimit && (
          <Select
            classNames={{
              trigger: '_rounded-l-none py-4 bg-default-200'
            }}
            radius='sm'
            variant='flat'
            // label='Размер страницы'
            items={limitItems}
            onSelectionChange={(v) => updateURLParams({ limit: Number(v.currentKey) })}
            // labelPlacement='outside'
            className='bh w-20 rounded-l-none py-1'
            size='sm'
            defaultSelectedKeys={[String(limit)]}
          >
            {(item) => <SelectItem key={item.key}>{item.label}</SelectItem>}
          </Select>
        )}
      </div>
    ))
  )
}
