import { test, expect } from '@playwright/test'

test.describe('ProfileSettings Component', () => {
  // Мокируем данные пользователя
  const mockClientData = {
    client_id: 1,
    client_number: 12345,
    client_name: 'Тест Тестов',
    client_mail: '<EMAIL>',
    client_phone: '+79001234567',
    client_country: 'Россия',
    client_city: 'Москва',
    client_street: 'Тестовая',
    client_house: '1',
    client_flat: '1',
    client_postindex: '123456',
    client_discount: 0,
    client_cdekid: '',
    remember_me_token: null,
    created_at: '2024-01-01',
    updated_at: '2024-01-01'
  }

  // Перед каждым тестом
  test.beforeEach(async ({ page }) => {
    // Мокируем API запросы
    await page.route('/api/auth/login/check', async (route) => {
      await route.fulfill({
        status: 200,
        body: JSON.stringify(mockClientData)
      })
    })

    // Переходим на страницу настроек
    await page.goto('/profile/settings')
    // Ждем загрузки компонента
    await page.waitForSelector('text=Настройки профиля')
  })

  test('должен отображать данные пользователя', async ({ page }) => {
    // Проверяем, что данные пользователя отображаются
    await expect(page.getByLabel('ФИО')).toHaveValue(mockClientData.client_name)
    await expect(page.getByLabel('Email')).toHaveValue(mockClientData.client_mail)
    await expect(page.getByLabel('Телефон')).toHaveValue(mockClientData.client_phone)
  })

  test('должен обновлять личные данные пользователя', async ({ page }) => {
    // Мокируем ответ на обновление
    await page.route('/api/client/update', async (route) => {
      const requestBody = JSON.parse(route.request().postData() || '{}')
      
      // Проверяем структуру запроса
      expect(requestBody).toHaveProperty('client')
      expect(requestBody.client).toHaveProperty('client_name')
      expect(requestBody.client).toHaveProperty('client_mail')
      expect(requestBody.client).toHaveProperty('client_phone')

      await route.fulfill({
        status: 200,
        body: JSON.stringify({ success: true })
      })
    })

    // Обновляем данные
    await page.getByLabel('ФИО').fill('Новый Тест Тестов')
    await page.getByLabel('Email').fill('<EMAIL>')
    await page.getByLabel('Телефон').fill('+79007654321')

    // Нажимаем кнопку сохранения
    await page.getByText('Сохранить').click()

    // Проверяем, что появилось уведомление об успехе
    await expect(page.getByText('Данные успешно обновлены')).toBeVisible()
  })

  test('должен обновлять данные юридического лица', async ({ page }) => {
    // Мокируем ответ на обновление
    await page.route('/api/client/update', async (route) => {
      const requestBody = JSON.parse(route.request().postData() || '{}')
      
      // Проверяем структуру запроса для юр.лица
      expect(requestBody).toHaveProperty('client')
      expect(requestBody).toHaveProperty('org')
      expect(requestBody.org).toHaveProperty('name')
      expect(requestBody.org).toHaveProperty('inn')
      expect(requestBody.org).toHaveProperty('kpp')
      expect(requestBody.org).toHaveProperty('address')
      expect(requestBody.org).toHaveProperty('bank')
      expect(requestBody.org).toHaveProperty('bik')
      expect(requestBody.org).toHaveProperty('rschet')
      expect(requestBody.org).toHaveProperty('kschet')

      await route.fulfill({
        status: 200,
        body: JSON.stringify({ success: true })
      })
    })

    // Включаем режим юр.лица
    await page.getByLabel('Юридическое лицо').click()

    // Заполняем данные организации
    await page.getByLabel('Название организации').fill('ООО Тест')
    await page.getByLabel('ИНН').fill('**********')
    await page.getByLabel('КПП').fill('*********')
    await page.getByLabel('Юридический адрес').fill('г. Москва, ул. Тестовая, д. 1')
    await page.getByLabel('Банк').fill('ПАО Сбербанк')
    await page.getByLabel('БИК').fill('*********')
    await page.getByLabel('Расчетный счет').fill('40702810**********12')
    await page.getByLabel('Корреспондентский счет').fill('30101810**********12')

    // Нажимаем кнопку сохранения
    await page.getByText('Сохранить').click()

    // Проверяем, что появилось уведомление об успехе
    await expect(page.getByText('Данные успешно обновлены')).toBeVisible()
  })

  test('должен показывать ошибку при неудачном обновлении', async ({ page }) => {
    // Мокируем ошибку при обновлении
    await page.route('/api/client/update', async (route) => {
      await route.fulfill({
        status: 400,
        body: JSON.stringify({ error: 'Ошибка обновления' })
      })
    })

    // Пытаемся обновить данные
    await page.getByLabel('ФИО').fill('Тест Ошибки')
    await page.getByText('Сохранить').click()

    // Проверяем, что появилось уведомление об ошибке
    await expect(page.getByText('Ошибка при обновлении данных, попробуйте позже')).toBeVisible()
  })
})