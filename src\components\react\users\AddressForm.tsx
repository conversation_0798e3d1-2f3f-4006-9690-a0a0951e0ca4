import { useEffect, useState } from 'react'
import { Input, Autocomplete, AutocompleteItem, Form, Button } from '@heroui/react'
import { useAsyncList } from '@react-stately/data'
import { useForm, Controller } from 'react-hook-form'
import type { Country, ShippingLocation } from '@/types/form'

interface AddressFormProps {
  data: any
  onChange: (e: { target: { name: string; value: any } }) => void
  handleNext: () => void
  onValidityChange?: (isValid: boolean) => void
  nextBtnTitle?: string
  iplocate?: boolean
  showNextPermanent?: boolean
  countries: Country[] // <--- добавляем проп
}

export const AddressForm = ({
  data: initialData,
  onChange,
  handleNext,
  onValidityChange,
  nextBtnTitle = 'Продолжить',
  iplocate = true,
  showNextPermanent = false,
  countries
}: AddressFormProps) => {

  function isEmptyCountryData(val: any) {
    return !val || (typeof val === 'object' && Object.keys(val).length === 0);
  }

  function getInitialData() {
    if (!countries || countries.length === 0) {
      return { ...initialData };
    }
    let countryData = initialData.countryData;
    // Если страна не указана вообще или countryData пустой объект, подставляем Россию по умолчанию
    if ((!initialData.country || initialData.country === '') && isEmptyCountryData(initialData.countryData)) {
      countryData = countries.find((c) => c.title === 'Россия') || {
        country_id: 643,
        iso: '643',
        title: 'Россия'
      };
      return {
        ...initialData,
        country: 'Россия',
        countryData
      };
    }
    // ...остальная логика поиска countryData по названию...
    if (initialData.country && isEmptyCountryData(initialData.countryData)) {
      const found = countries.find((c) => c.title === initialData.country)
      if (found) {
        countryData = found
      } else {
        countryData = countries.find((c) => c.title === 'Россия') || countries[0]
      }
    }
    if (!countryData) {
      countryData = countries.find((c) => c.title === 'Россия') || countries[0]
    }
    return {
      ...initialData,
      country: initialData.country || countryData.title,
      countryData
    }
  }

  const { control, handleSubmit, setValue, watch, formState, trigger } = useForm({
    defaultValues: getInitialData(),
    mode: 'onChange'
  })

  const [showNextBtn, setShowNextBtn] = useState(true)

  useEffect(() => {
    if (!countries || countries.length === 0) return;
    // Если страна не указана вообще или countryData пустой объект
    if (
      (!initialData.country || initialData.country === '') && isEmptyCountryData(initialData.countryData)
    ) {
      const countryData = countries.find((c) => c.title === 'Россия') || countries[0]
      onChange({ target: { name: 'country', value: { title: countryData.title, countryData } } })
      return;
    }
    // Если есть только строка страны, а countryData пустой объект
    if (
      initialData.country && isEmptyCountryData(initialData.countryData)
    ) {
      const found = countries.find((c) => c.title === initialData.country)
      const countryData = found || countries.find((c) => c.title === 'Россия') || countries[0]
      if (
        isEmptyCountryData(initialData.countryData) ||
        initialData.countryData.title !== countryData.title
      ) {
        onChange({ target: { name: 'country', value: { title: countryData.title, countryData } } })
      }
    }
  }, [countries, initialData.country, initialData.countryData, onChange])

  const locationList = useAsyncList<ShippingLocation>({
    async load({ filterText }) {
      if (!filterText) return { items: [] }
      const resp = await fetch('/api/service/findlocation', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ query: filterText, location: filterText })
      })
      if (!resp.ok) return { items: [] }
      const items = await resp.json()
      return { items: items.map((item: any) => ({ ...item, uniqueId: `${item.location}-${item.city_fias_id || ''}` })) }
    }
  })

  const streetList = useAsyncList<ShippingLocation>({
    async load({ filterText }) {
      if (!filterText || !watch('location')) return { items: [] }
      const resp = await fetch('/api/service/findstreet', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ query: filterText, location: watch('location') })
      })
      if (!resp.ok) return { items: [] }
      const items = await resp.json()
      return { items: items.map((item: any, i: number) => ({ ...item, uniqueId: `${item.street_with_type}-${item.city_fias_id || ''}-${i}` })) }
    }
  })

  useEffect(() => {
    const isFormValid =
      formState.isValid &&
      !!watch('country') &&
      !!watch('location') &&
      !!watch('street') &&
      !!watch('house') &&
      !!watch('zipCode') &&
      /^\d{6}$/.test(watch('zipCode') || '')

    onValidityChange?.(isFormValid)
  }, [formState.isValid])

  const handleCountrySelect = (countryId: any) => {
    const selected = countries.find((c) => c.country_id.toString() === countryId)
    if (selected) {
      setValue('country', selected.title)
      setValue('countryData', selected)
      onChange({ target: { name: 'country', value: { title: selected.title, countryData: selected } } })

      setTimeout(() => {
        trigger('country')
      }, 0)
    } else {
      onChange({ target: { name: 'country', value: { title: '', countryData: {} } } })

    }
  }

  const handleLocationSelect = (key: any) => {
    const selected = locationList.items.find((i) => i.uniqueId === key)
    if (selected) {
      setValue('location', selected.location)
      setValue('locationData', selected)
      setValue('street', '')
      setValue('streetData', null)

      if (selected.index) {
        setValue('zipCode', selected.index)
      }

      const patchData = {
        location: selected.location,
        locationData: selected,
        street: '',
        streetData: null,
        zipCode: selected.index || ''
      }

      onChange({
        target: {
          name: 'addressPatch',
          value: patchData
        }
      })

      setTimeout(() => {
        trigger('zipCode')
      }, 0)
    }
  }

  const handleStreetSelect = (key: any) => {
    const selected = streetList.items.find((i) => i.uniqueId === key)
    if (selected) {
      setValue('street', selected.street_with_type)
      setValue('streetData', selected)

      const currentZipCode = watch('zipCode')

      const zipCode = selected.index || currentZipCode || ''
      if (selected.index) {
        setValue('zipCode', selected.index)
      }

      const patchData = {
        street: selected.street_with_type,
        streetData: selected,
        zipCode: zipCode
      }

      onChange({
        target: {
          name: 'addressPatch',
          value: patchData
        }
      })

      setTimeout(() => {
        trigger('zipCode')
      }, 0)
    }
  }

  const onSubmit = (data: any) => {
    const isFormValid = Object.keys(formState.errors).length === 0

    if (isFormValid) {
      handleNext?.()

      if (!showNextPermanent) {
        setTimeout(() => trigger('reset'), 0)
        setShowNextBtn(false)
      }
    }
  }

  return (
    <Form onSubmit={handleSubmit(onSubmit)}>
      <div className='flex w-full flex-wrap gap-4'>
        <Controller
          control={control}
          name='country'
          rules={{ required: 'Выберите страну' }}
          render={({ field: { ref, value }, fieldState: { invalid, error } }) => (
            <Autocomplete
              ref={ref}
              label='Страна'
              placeholder='Выберите страну'
              showScrollIndicators
              isRequired
              className='w-52'
              scrollShadowProps={{ isEnabled: false }}
              defaultItems={countries}
              selectedKey={countries.find((c) => c.title === value)?.country_id?.toString()}
              value={value}
              onInputChange={(val) => {
                setValue('country', val)
                onChange({ target: { name: 'country', value: val } })
                trigger('country')
              }}
              onSelectionChange={handleCountrySelect}
              onClose={() => trigger('country')}
              isInvalid={invalid}
              errorMessage={error?.message || ''}
            >
              {(country) => <AutocompleteItem key={country.country_id?.toString()}>{country.title}</AutocompleteItem>}
            </Autocomplete>
          )}
        />

        <Controller
          control={control}
          name='location'
          rules={{ required: 'Выберите или введите населённый пункт' }}
          render={({ field: { ref, value }, fieldState: { invalid, error } }) => (
            <Autocomplete
              ref={ref}
              label='Населённый пункт'
              isRequired
              variant='flat'
              items={locationList.items}
              isLoading={locationList.isLoading}
              allowsCustomValue
              inputValue={value}
              onInputChange={(val) => {
                setValue('location', val)
                locationList.setFilterText(val)
                onChange({
                  target: {
                    name: 'addressPatch',
                    value: {
                      location: val,
                      locationData: null
                    }
                  }
                })
              }}
              onSelectionChange={handleLocationSelect}
              isInvalid={invalid}
              errorMessage={error?.message}
            >
              {(item) => <AutocompleteItem key={item.uniqueId}>{item.location}</AutocompleteItem>}
            </Autocomplete>
          )}
        />

        <Controller
          control={control}
          name='street'
          rules={{ required: 'Выберите или введите улицу' }}
          render={({ field: { ref, value }, fieldState: { invalid, error } }) => (
            <Autocomplete
              ref={ref}
              label='Улица'
              isRequired
              variant='flat'
              items={streetList.items}
              isLoading={streetList.isLoading}
              allowsCustomValue
              inputValue={value}
              onInputChange={(val) => {
                setValue('street', val)
                streetList.setFilterText(val)
                onChange({
                  target: {
                    name: 'addressPatch',
                    value: {
                      street: val,
                      streetData: null
                    }
                  }
                })
              }}
              onSelectionChange={handleStreetSelect}
              isInvalid={invalid}
              errorMessage={error?.message}
            >
              {(item) => <AutocompleteItem key={item.uniqueId}>{item.street_with_type}</AutocompleteItem>}
            </Autocomplete>
          )}
        />

        <div className='grid grid-cols-2 gap-4'>
          <Controller
            control={control}
            name='house'
            rules={{ required: 'Введите номер дома' }}
            render={({ field: { ref, ...field }, fieldState: { invalid, error } }) => (
              <Input
                {...field}
                onChange={(e) => {
                  field.onChange(e)
                  onChange({ target: { name: 'house', value: e.target.value } })
                }}
                label='Дом'
                isRequired
                validationBehavior='aria'
                isInvalid={invalid}
                errorMessage={error?.message}
              />
            )}
          />
          <Controller
            control={control}
            name='apartment'
            render={({ field: { ref, ...field } }) => (
              <Input
                {...field}
                onChange={(e) => {
                  field.onChange(e)
                  onChange({ target: { name: 'apartment', value: e.target.value } })
                }}
                label='Квартира/Офис'
                validationBehavior='aria'
              />
            )}
          />
        </div>
        <Controller
          control={control}
          name='zipCode'
          rules={{
            required: 'Введите почтовый индекс',
            pattern: { value: /^\d{6}$/, message: 'Введите корректный индекс (6 цифр)' }
          }}
          render={({ field: { ref, ...field }, fieldState: { invalid, error } }) => (
            <Input
              {...field}
              onChange={(e) => {
                const newValue = e.target.value
                field.onChange(e)

                setTimeout(() => {
                  trigger('zipCode')

                  const isValidZipCode = /^\d{6}$/.test(newValue)

                  // console.log('AddressForm: Обновление индекса:', {
                  //   newValue,
                  //   isValidZipCode
                  // })

                  const customEvent = {
                    target: {
                      name: 'zipCode',
                      value: newValue,
                      isValid: isValidZipCode
                    }
                  }
                  onChange(customEvent)
                }, 0)
              }}
              label='Почтовый индекс'
              placeholder='123456'
              isRequired
              className='w-40'
              validationBehavior='aria'
              isInvalid={invalid}
              errorMessage={error?.message}
            />
          )}
        />
      </div>
      <div className='mt-4 flex w-full justify-end'>
        {showNextBtn && (
          <Button color='warning' variant='flat' type='submit'>
            {nextBtnTitle || 'Продолжить'}
          </Button>
        )}
      </div>
    </Form>
  )
}
