import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { httpBatchLink } from '@trpc/client'
import { useEffect, useState } from 'react'
import { trpcEndPoint, trpcReact } from '../../trpc'
import type { AppRouter } from '../../rti-api/app/Services/tRPC'

export const TrpcReactWrapper = ({ children }) => {
  const [queryClient] = useState(() => new QueryClient({defaultOptions: {
    
    queries: {
      // staleTime: 60 * 1000,
    }
  }}))
  const [trpcClient] = useState(() =>
    trpcReact.createClient<AppRouter>({
      links: [
        httpBatchLink({
          //TODO: VITE_URLa
          url: trpcEndPoint,
          
          // // You can pass any HTTP headers you wish here
          // async headers() {
          //     return {
          //         authorization: getAuthCookie(),
          //     };
          // },
        })
      ],
    })
  )
  

  useEffect(() => {
    //console.log('@TRCP  Wrapper mounted: ', trpcEndPoint)
  }, [])

  //console.log('@TRCP  Wrapper load')
  

  return (
    <trpcReact.Provider  client={trpcClient} queryClient={queryClient}>
      <QueryClientProvider  client={queryClient}>
        <div className='trcp-wrapper'>
          {children}
        </div>
      </QueryClientProvider>
    </trpcReact.Provider>
  )
}
