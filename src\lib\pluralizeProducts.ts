// import {  pluralize } from 'numeralize-ru'

export const pluralizeProducts = (count: number) => {
  // return numeralize(count, Gender.Masculine, Case.Nominative)
  // return pluralize(count, 'товар', 'товара', 'товаров')

  const mod100 = Math.abs(count) % 100
  const mod10 = mod100 % 10

  if (mod10 === 0 || mod10 >= 5 || (mod100 >= 11 && mod100 <= 19)) {
    return 'товаров'
  }

  if (mod10 === 1) {
    return 'товар'
  }

  return 'товара'
}
