---
import { priceFormat } from "@/lib/priceFormat"
import { ProductSearchCard } from "./react/ProductSearchCard"
import { QtyInput } from "./react/QtyInput"

const { data } = Astro.props
---

<div class='flex flex-col gap-5 rounded-lg border sm:mt-2 sm:p-2'>
  {data?.products?.map((product, index) => (
    <div>
      <ProductSearchCard columns={data?.categoriesData?.[String(product.prod_cat)]?.columns} client:visible product={product} ind={index + 1}>
        <div slot='addToCartComponent'>
          <QtyInput size='sm' client:only='react' product={product}>
            <div slot='fallback'>
              <div class='h-10 w-28 animate-pulse rounded-lg bg-warning-100 dark:bg-default-50' />
            </div>
            <div slot='label'>{priceFormat(product.prod_price)}</div>
          </QtyInput>
        </div>
      </ProductSearchCard>
    </div>
  ))}
</div> 