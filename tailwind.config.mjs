/** @type {import('tailwindcss').Config} */

// const { heroui } = require('@heroui/react')
const { heroui } = require('@heroui/react')

export default {
  darkMode: ['class'],
  content: [
    './src/**/*.{astro,html,js,jsx,md,mdx,svelte,ts,tsx,vue}',
    // './node_modules/@heroui/theme/dist/**/*.{js,ts,jsx,tsx}'
    './node_modules/@heroui/theme/dist/**/*.{js,ts,jsx,tsx}'
  ],

  'layout': {
    'disabledOpacity': '0.6'
  },
  plugins: [
    heroui({
      themes: {
        dark: {
          colors: {
            background: {}
          }
        },
      },
      layout: {
        radius: {
          // small: 0,
          // large: 0,
          // medium: 0
        }
      }
    }),
    // require('daisyui', {}),
    require('tailwindcss-animate'),
    require('tailwind-scrollbar')({ nocompatible: true, preferredStrategy: 'pseudoelements' })
  ],
  daisyui: {
    themes: false, // false: only light + dark | true: all themes | array: specific themes like this ["light", "dark", "cupcake"]
    darkTheme: 'dark', // name of one of the included themes for dark mode
    base: false, // applies background color and foreground color for root element by default
    styled: true, // include daisyUI colors and design decisions for all components
    utils: true, // adds responsive and modifier utility classes
    prefix: 'd-', // prefix for daisyUI classnames (components, modifiers and responsive class names. Not colors)
    logs: true, // Shows info about daisyUI version and used config in the console when building your CSS
    themeRoot: ':root' // The element that receives theme color CSS variables
  }
}
