import React from 'react'
import { But<PERSON> } from '@heroui/react'
import { FileSpreadsheetIcon, RefreshCwIcon, UploadIcon } from 'lucide-react'

interface FileUploaderProps {
  onUpload: (files: File[]) => void
  accept?: string
  multiple?: boolean
  maxSize?: number
}

export const FileUploader: React.FC<FileUploaderProps> = ({
  onUpload,
  accept = '.xlsx,.csv,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel,text/csv',
  multiple = false,
  maxSize = 10485760 // 10MB
}) => {
  const fileInputRef = React.useRef<HTMLInputElement>(null)
  const [isDragging, setIsDragging] = React.useState(false)
  const [selectedFile, setSelectedFile] = React.useState<File | null>(null)

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files && files.length > 0) {
      validateAndUpload(Array.from(files))
    }
  }

  const validateAndUpload = (files: File[]) => {
    const validFiles = files.filter((file) => file.size <= maxSize)
    if (validFiles.length > 0) {
      setSelectedFile(validFiles[0])
      onUpload(validFiles)
    } else {
      console.error('File size exceeds the maximum allowed size')
    }
  }

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    setIsDragging(true)
  }

  const handleDragLeave = () => {
    setIsDragging(false)
  }

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    setIsDragging(false)
    const files = e.dataTransfer.files
    if (files && files.length > 0) {
      validateAndUpload(Array.from(files))
    }
  }

  const triggerFileInput = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click()
    }
  }

  return (
    <div className='w-full'>
      <div
        className={`flex items-center justify-center rounded-lg border border-dashed p-4 transition-colors ${
          isDragging ? 'border-primary bg-primary-50/20' : 'border-default-200 hover:border-primary-300'
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <input type='file' ref={fileInputRef} className='hidden' accept={accept} multiple={multiple} onChange={handleFileChange} />
        {selectedFile ? (
          <div className='flex items-center gap-2'>
            <FileSpreadsheetIcon className='h-5 w-5 text-primary' />
            <span className='text-sm text-default-700'>{selectedFile.name}</span>
            <Button size='sm' variant='light' color='primary' isIconOnly onPress={triggerFileInput} className='ml-2'>
              <RefreshCwIcon className='h-4 w-4' />
            </Button>
          </div>
        ) : (
          <Button color='primary' variant='flat' startContent={<UploadIcon className='h-4 w-4' />} onPress={triggerFileInput}>
            Выбрать XLSX/CSV
          </Button>
        )}
      </div>
    </div>
  )
}

export default FileUploader
