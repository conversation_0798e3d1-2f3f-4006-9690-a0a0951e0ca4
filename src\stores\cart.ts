import type { CartSums } from '@/types/CartSum'
import { Derived, Store } from '@tanstack/store'

export type CartItem = {
  prod_id: number
  qty: number
}

type CartType = {
  [prod_id: string | number]: CartItem
}

type UpdCartItem = Pick<CartItem, 'prod_id' | 'qty'>

export const whosalePrices = new Store(false)
export const cartItems = new Store<CartType>({})
export const cartSums = new Store<CartSums>({})

export const cartIsLoading = new Store(false)

export function setCartIsLoading(value: boolean) {
  cartIsLoading.setState(() => value)
}

export function setCartSums(data: CartSums) {
  cartSums.setState(() => data)
}
// export const cartTotal = new Store(0)

export function setWhosalePrices(value: boolean) {
  whosalePrices.setState(() => value)
}

export const getCartTotal = () => {
  return cartTotal.state
}

export function getQty(prod_id: number | string) {
  return cartItems.state[prod_id]?.qty || 0
}
export function getCartItem(prod_id: number | string): CartItem | undefined {
  return cartItems.state[prod_id]
}

export async function fillCart(items: CartItem[]) {
  if (items?.length > 0) {
    cartItems.setState(() => ({
      ...items.reduce((acc, item) => {
        acc[item.prod_id] = item
        return acc
      }, {} as CartType)
    }))
  }
}

export async function updateCartItem({ prod_id, qty }: UpdCartItem) {
  //console.log('🚀 ~ updateCartItem ~ { prod_id, qty }:', { prod_id, qty })
  const stringId = String(prod_id)

  if (qty < 1) {
    cartItems.setState((prev) => {
      const newState = { ...prev }
      delete newState[stringId]
      return newState
    })
  } else {
    cartItems.setState((prev) => ({
      ...prev,
      [stringId]: { prod_id, qty }
    }))
  }
}

export const cartTotal = new Derived({
  // fn: () => Object.values(cartItems.state).reduce((acc, item) => acccartTotal + item.qty, 0),
  fn: () => Object.values(cartItems.state).length,
  deps: [cartItems]
})

// Must mount the derived value to start listening for updates
const unmountCartTotal = cartTotal.mount()

cartTotal.subscribe((val) => {
  //console.log('@change cartTotal: ', val)
})

cartItems?.subscribe((val) => {
  //console.log('@change cartItems: ', val)
})

export function clearCart() {
  cartItems.setState({})
}
