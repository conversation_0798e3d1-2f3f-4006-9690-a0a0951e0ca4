import { Form, Input, Button, Autocomplete, AutocompleteItem, Divider } from '@heroui/react'
import { useState, useEffect, Fragment, useRef, type ChangeEvent, type ComponentProps } from 'react'
import { defineStepper } from '@stepperize/react'
import { motion, AnimatePresence, m, LazyMotion, domAnimation } from 'framer-motion'
import { useAsyncList } from '@react-stately/data'

// Импорт вынесённых компонентов
import { AccountForm } from './AcccountForm'
import { PersonalForm } from './PersonalForm'
import { AddressForm } from './AddressForm'
import type { FormData } from '@/types/form'

const STORAGE_KEY = 'registration_form_data'

// Функция для получения сохраненных данных из localStorage
const getSavedFormData = (): { formData: FormData; stepId: string } | null => {
    try {
        const savedData = localStorage.getItem(STORAGE_KEY)
        return savedData ? JSON.parse(savedData) : null
    } catch (error) {
        console.error('Error reading from localStorage:', error)
        return null
    }
}

// Функция для сохранения данных в localStorage
const saveFormData = (formData: FormData, stepId: string) => {
    try {
        localStorage.setItem(STORAGE_KEY, JSON.stringify({ formData, stepId }))
    } catch (error) {
        console.error('Error saving to localStorage:', error)
    }
}

const CheckIcon = (props: ComponentProps<'svg'>) => {
    return (
        <svg {...props} fill='none' stroke='currentColor' strokeWidth={2} viewBox='0 0 24 24'>
            <m.path
                animate={{ pathLength: 1 }}
                d='M5 13l4 4L19 7'
                initial={{ pathLength: 0 }}
                strokeLinecap='round'
                strokeLinejoin='round'
                transition={{
                    delay: 0.2,
                    type: 'tween',
                    ease: 'easeOut',
                    duration: 0.3
                }}
            />
        </svg>
    )
}

const { useStepper, steps, utils } = defineStepper(
    {
        id: 'account',
        title: 'Учетные данные',
        description: 'Создание аккаунта'
    },
    {
        id: 'personal',
        title: 'Личная информация',
        description: 'Контактные данные'
    },
    {
        id: 'address',
        title: 'Адрес доставки',
        description: 'Данные для доставки'
    }
)

export const RegistrationForm = () => {
    const stepper = useStepper()
    const currentIndex = utils.getIndex(stepper.current.id)
    const stepsRef = useRef<(HTMLLIElement | null)[]>([])

    const [formData, setFormData] = useState<FormData>(() => {
        const savedData = getSavedFormData()
        if (savedData) return savedData.formData

        return {
            email: '',
            password: '',
            firstName: '',
            lastName: '',
            phone: '',
            country: '',
            countryData: null,
            location: '',
            locationData: null,
            street: '',
            house: '',
            apartment: '',
            zipCode: ''
        }
    })

    // Восстанавливаем шаг из localStorage при первой загрузке
    useEffect(() => {
        const savedData = getSavedFormData()
        if (savedData) {
            stepper.goTo(savedData.stepId)
        }
    }, [])

    // Сохраняем данные при изменении формы или шага
    useEffect(() => {
        saveFormData(formData, stepper.current.id)
    }, [formData, stepper.current.id])

    const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target
        setFormData((prev) => ({
            ...prev,
            [name]: value
        }))
    }

    const validateCurrentStep = () => {
        const form = document.querySelector('form')
        if (!form) return false
        return form.reportValidity()
    }

    const scrollToStep = (index: number) => {
        const stepElement = stepsRef.current[index]
        if (stepElement) {
            stepElement.scrollIntoView({
                behavior: 'smooth',
                block: 'center'
            })
        }
    }

    const handleNext = () => {
        if (validateCurrentStep()) {
            stepper.next()
            scrollToStep(currentIndex + 1)
        }
    }

    const handlePrev = () => {
        stepper.prev()
        scrollToStep(currentIndex - 1)
    }

    const handleStepClick = (index: number, stepId: string) => {
        if (index <= currentIndex) {
            stepper.goTo(stepId)
            scrollToStep(index)
        }
    }

    const handleSubmit = () => {
        if (validateCurrentStep()) {
            //console.log('Form submitted:', formData)
            localStorage.removeItem(STORAGE_KEY)
        }
    }

    // Анимация для плавного появления контента
    const contentVariants = {
        initial: { opacity: 0, x: 20 },
        animate: { opacity: 1, x: 0 },
        exit: { opacity: 0, x: -20, position: 'absolute' }
    }

    return (
        <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ duration: 0.3 }} className='mx-auto w-full max-w-[600px] space-y-6'>
            <div className='flex items-center justify-between'>
                <h2 className='text-xl font-semibold'>Регистрация</h2>
                <div className='flex items-center gap-2'>
                    <span className='text-muted-foreground text-sm'>
                        Шаг {currentIndex + 1} из {steps.length}
                    </span>
                </div>
            </div>
            {/*  */}

            <Form validationBehavior='native' onSubmit={(e) => e.preventDefault()}>
                <nav aria-label='Registration Steps' className='group my-4'>
                    <ol className='flex flex-col gap-2' aria-orientation='vertical'>
                        {stepper.all.map((step, index, array) => (
                            <Fragment key={step.id}>
                                <LazyMotion features={domAnimation}>
                                    <li ref={(el) => (stepsRef.current[index] = el)} className='flex flex-shrink-0 items-center gap-4'>
                                        <Button
                                            type='button'
                                            isIconOnly
                                            variant={index <= currentIndex ? 'flat' : 'ghost'}
                                            aria-current={stepper.current.id === step.id ? 'step' : undefined}
                                            aria-posinset={index + 1}
                                            aria-setsize={steps.length}
                                            aria-selected={stepper.current.id === step.id}
                                            onPress={() => handleStepClick(index, step.id)}
                                        >
                                            {currentIndex > index ? <CheckIcon className='w-6 text-default-900' /> : index + 1}
                                        </Button>
                                        <div className='flex flex-col'>
                                            <span className='text-sm font-semibold'>{step.title}</span>
                                            <span className='text-sm text-default-600'>{step.description}</span>
                                        </div>
                                    </li>
                                </LazyMotion>

                                <div className='flex gap-4'>
                                    {index < array.length - 1 && (
                                        <div
                                            className='flex justify-center'
                                            style={{
                                                paddingInlineStart: '1.25rem'
                                            }}
                                        >
                                            <Divider orientation='vertical' />
                                        </div>
                                    )}
                                    <div className='relative my-4 flex-1'>
                                        <AnimatePresence initial={false} mode='wait'>
                                            {stepper.current.id === step.id && (
                                                <motion.div
                                                    key={step.id}
                                                    variants={contentVariants}
                                                    initial='initial'
                                                    animate='animate'
                                                    exit='exit'
                                                    transition={{
                                                        type: 'spring',
                                                        stiffness: 200,
                                                        damping: 25,
                                                        mass: 0.5
                                                    }}
                                                    className='w-full space-y-4'
                                                >
                                                    {stepper.switch({
                                                        account: () => <AccountForm data={formData} onChange={handleInputChange} />,
                                                        personal: () => <PersonalForm data={formData} onChange={handleInputChange} />,
                                                        address: () => <AddressForm data={formData} onChange={handleInputChange} />
                                                    })}
                                                    <div className='mt-4 flex justify-end gap-4'>
                                                        {!stepper.isFirst && (
                                                            <Button type='button' variant='ghost' onPress={handlePrev}>
                                                                Назад
                                                            </Button>
                                                        )}
                                                        {!stepper.isLast ? (
                                                            <Button type='button' variant='flat' onPress={handleNext}>
                                                                Продолжить
                                                            </Button>
                                                        ) : (
                                                            <Button type='button' variant='flat' onPress={handleSubmit}>
                                                                Завершить регистрацию
                                                            </Button>
                                                        )}
                                                    </div>
                                                </motion.div>
                                            )}
                                        </AnimatePresence>
                                    </div>
                                </div>
                            </Fragment>
                        ))}
                    </ol>
                </nav>
            </Form>
        </motion.div>
    )
}
