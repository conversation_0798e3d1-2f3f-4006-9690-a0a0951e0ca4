const trns = {
  prod_sku: 'Артикул',
  prod_analogsku: 'Аналог',
  prod_analogs: 'А<PERSON><PERSON>ог<PERSON>',
  prod_manuf: 'Производитель',
  prod_purpose: 'Назначение',
  prod_cat: 'Категория',
  prod_price: 'Цена розн.',
  prod_year: 'Год',
  prod_model: 'Модель',
  prod_type: 'Тип',
  prod_id: 'ID',
  prod_count: 'Наличие',
  prod_note: 'Примечание',
  prod_uses: 'Применение',
  prod_size: 'Размер',
  prod_material: 'Материал',
  prod_group: 'Группа',
  prod_group_count: 'Наличие по группе',
  prod_group_price: 'Цена по группе',
  prod_img: 'Изобр.',
  prod_img_rumi: 'Изобр.rumi',
  whosaleprice: 'Цена опт',
  size_h_2: 'Высота/2',
  images: 'Доп.изобр'
}

export function trnColumns(col: string) {
  return trns[col] || '_'
}
