import { Tooltip } from "@heroui/react"
import { CameraIcon } from "lucide-react"
import { getRtiImageUrl } from '@/lib/config'

interface Props {
  product: any
}

export const PhotoTooltip = ({ product }: Props) => {
  return (
    <Tooltip
      size='sm'
      shadow='lg'
      closeDelay={0}
      showArrow
      motionProps={{
        variants: {
          exit: {
            opacity: 0,
            transition: {
              duration: 0.05,
              ease: 'backIn'
            }
          },
          enter: {
            opacity: 1,
            transition: {
              duration: 0.05,
              ease: 'backOut'
            }
          }
        }
      }}
      disableAnimation
      classNames={{ base: 'pointer-events-none', content: 'p-2 rounded-lg shadow-xl' }}
      content={<img className='rounded-lg' width='184px' src={getRtiImageUrl(`${product?.prod_img || product?.prod_analogsku}.jpg`)} />}
    >
      <CameraIcon />
    </Tooltip>
  )
}
