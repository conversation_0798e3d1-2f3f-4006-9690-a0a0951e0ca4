---
import { CFilters } from '@/components/react/CFilters'
import { trpc } from '@/trpc'
import type { CategoryData } from '@/types/GetCategoryProduct'

type FiltersDataType = Awaited<ReturnType<typeof trpc.products.getFiltersData.query>>

interface Props {
  filtersList?: CategoryData['category']['filters']
  categoryId?: number | string | string[] | number[]
  initialFilterValue?: Record<string, string[]>
  filters?: unknown
  search?: string
  isMobile?: boolean
  isSticky?: boolean
}

const { categoryId, initialFilterValue = {}, search, filters, isMobile = false, isSticky = true } = Astro.props

let filtersData: FiltersDataType | undefined = undefined
if (categoryId) {
  filtersData = await trpc.products.getFiltersDataMeili.query({
    identifier: Array.isArray(categoryId) ? [...new Set(categoryId.map(id => typeof id === 'string' ? parseInt(id) : id))] : categoryId,
    search: search || '',
    filters
  })
}

// Функция для определения активности фильтра
function isFilterActive(filterKey: string, initialValues: Record<string, string[]>): boolean {
  const filterValue = initialValues?.[filterKey]
  return Array.isArray(filterValue) && filterValue.length > 0
}

// Функция для сортировки ключей фильтров (активные первыми)
function getSortedFilterKeys(filtersData: FiltersDataType | undefined, initialValues: Record<string, string[]>): string[] {
  if (!filtersData) return []

  const filterKeys = Object.keys(filtersData)

  // Разделяем на активные и неактивные фильтры
  const activeFilters: string[] = []
  const inactiveFilters: string[] = []

  filterKeys.forEach(key => {
    if (isFilterActive(key, initialValues)) {
      activeFilters.push(key)
    } else {
      inactiveFilters.push(key)
    }
  })

  // Возвращаем активные фильтры первыми, затем неактивные
  // Сохраняем исходный порядок внутри каждой группы для стабильности
  return [...activeFilters, ...inactiveFilters]
}

// Получаем отсортированные ключи фильтров
const sortedFilterKeys = getSortedFilterKeys(filtersData, initialFilterValue)
---

{
  isMobile ? (
    <div class='overflow-x-auto pb-4'>
      <div class='flex gap-2 px-4'>
        {filtersData &&
          sortedFilterKeys.map((key) => (
            <div class='shrink-0'>
              <CFilters
                initState={initialFilterValue?.[key]}
                field={key as 'prod_type' | 'prod_manuf' | 'prod_purpose' | 'prod_cat'}
                client:only='react'
                values={filtersData[key].values}
                label={filtersData[key].title}
                isMobile={true}
              />
            </div>
          ))}
      </div>
    </div>
  ) : (
    <div
      class={`${isSticky ? 'sticky top-1' : ''} space-y-5 overflow-x-auto overflow-y-auto scrollbar-thin scrollbar-track-default-200 scrollbar-thumb-default-500`}
    >
      {filtersData &&
        sortedFilterKeys.map((key) => (
          <div class='pp-card p-2'>
            <CFilters
              initState={initialFilterValue?.[key]}
              field={key as 'prod_type' | 'prod_manuf' | 'prod_purpose' | 'prod_cat'}
              client:visible
              values={filtersData[key].values}
              label={filtersData[key].title}
              isMobile={false}
            />
          </div>
        ))}
    </div>
  )
}
