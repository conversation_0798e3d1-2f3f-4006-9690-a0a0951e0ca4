import { But<PERSON>, Checkbox, <PERSON>boxGroup, Input, Drawer, Drawer<PERSON>ooter, <PERSON>er<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DrawerHeader } from '@heroui/react'
import { useVirtualizer } from '@tanstack/react-virtual'
import { DeleteIcon, SearchIcon } from 'lucide-react'
import { useEffect, useRef, useState, useMemo, useCallback } from 'react'
import { updateURLParams } from '@/stores/qs'
import { defaultAliases } from '@/types/aliases'

interface Props {
  onValueChange?: (value: string[]) => void
  values: string[]
  field?: 'prod_type' | 'prod_manuf' | 'prod_purpose' | 'prod_cat'
  label: string
  initState?: string[]
  isMobile?: boolean
  aliases?: Record<'prod_type' | 'prod_manuf' | 'prod_purpose' | 'prod_cat', Record<string, string>>
}

export const CFilters = ({
  onValueChange,
  values = [],
  field,
  label,
  initState = [],
  isMobile = false,
  aliases = { prod_type: {}, prod_manuf: {}, prod_purpose: {}, prod_cat: {}, ...defaultAliases }
}: Props) => {
  const [items, setItems] = useState([...values])
  const [filteredItems, setFilteredItems] = useState(items)
  const [selected, setSelected] = useState(initState || [])
  const [search, setSearch] = useState('')
  const [isOpen, setIsOpen] = useState(false)

  // Sync items with external values
  useEffect(() => {
    setItems(values)
  }, [values])

  // Handle browser back button
  useEffect(() => {
    if (!isOpen) return

    const handlePopState = () => setIsOpen(false)
    window.history.pushState(null, '', window.location.href)
    window.addEventListener('popstate', handlePopState)

    return () => window.removeEventListener('popstate', handlePopState)
  }, [isOpen])

  // Filter items
  useEffect(() => {
    const searchTerm = search.toLowerCase()
    const filtered = items.filter((item) => {
      const originalValue = item?.toLowerCase()
      const aliasValue = aliases[field]?.[item]?.toLowerCase()

      return originalValue?.includes(searchTerm) || aliasValue?.includes(searchTerm)
    })

    // Сортируем так, чтобы выбранные элементы были первыми
    const sortedFiltered = [...filtered].sort((a, b) => {
      const aSelected = selected.includes(a)
      const bSelected = selected.includes(b)
      if (aSelected && !bSelected) return -1
      if (!aSelected && bSelected) return 1
      return 0
    })

    setFilteredItems(sortedFiltered)
  }, [search, items, selected]) // Добавляем selected в зависимости

  const applyFilters = useCallback(() => {
    updateURLParams({ filters: { [field]: selected } })
    setIsOpen(false)
  }, [field, selected])

  const onChangeHandler = useCallback(
    (v: string[]) => {
      setSelected(v)
      onValueChange?.(v)
      setSearch('')

      if (!isMobile) {
        updateURLParams({ filters: { [field]: v } })
      }
    },
    [field, isMobile, onValueChange]
  )

  const clearSelected = useCallback(() => {
    onChangeHandler([])
    updateURLParams({ filters: { [field]: null } })
  }, [field, onChangeHandler])

  const List = useCallback(() => {
    const parentRef = useRef<HTMLDivElement>(null)

    const virtualizer = useVirtualizer({
      count: filteredItems.length,
      getScrollElement: () => parentRef.current,
      estimateSize: () => 40,
      overscan: 5
    })

    return (
      <div
        className='h-[20vh] overflow-y-auto scrollbar-thin scrollbar-track-default-200 scrollbar-thumb-default-500 scrollbar-track-rounded-full scrollbar-thumb-rounded-full dark:scrollbar-track-default-100 dark:scrollbar-thumb-default-300'
        ref={parentRef}
      >
        <CheckboxGroup radius='sm' color='default' value={selected} onValueChange={onChangeHandler}>
          <div
            style={{
              height: `${virtualizer.getTotalSize()}px`,
              position: 'relative',
              width: '100%'
            }}
          >
            {virtualizer.getVirtualItems().map((virtualRow) => (
              <div
                key={virtualRow.index}
                style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '100%',
                  height: `${virtualRow.size}px`,
                  transform: `translateY(${virtualRow.start}px)`
                }}
              >
                <Checkbox value={filteredItems[virtualRow.index]} classNames={{ label: 'text-sm truncate' }}>
                  {aliases[field]?.[filteredItems[virtualRow.index]] || filteredItems[virtualRow.index]}
                </Checkbox>
              </div>
            ))}
          </div>
        </CheckboxGroup>
        {filteredItems.length === 0 && <div className='py-2 text-center text-sm text-default-700'>Нет результатов</div>}
      </div>
    )
  }, [filteredItems, selected, onChangeHandler, aliases, field])

  const FilterContent = useMemo(
    () => (
      <div className='space-y-3'>
        <Input
          isClearable
          radius='sm'
          startContent={<SearchIcon className='h-4 w-4' />}
          variant='flat'
          value={search}
          onValueChange={setSearch}
          placeholder='Поиск'
          classNames={{ input: 'text-sm' }}
        />
        <List />
      </div>
    ),
    [search, List]
  )

  if (isMobile) {
    return (
      <>
        <Button
          variant={selected.length ? 'solid' : 'flat'}
          color={selected.length ? 'warning' : 'default'}
          radius='sm'
          size='md'
          onPress={() => setIsOpen(true)}
        >
          {label} {selected.length > 0 && `(${selected.length})`}
        </Button>

        <Drawer
          isOpen={isOpen}
          onClose={() => setIsOpen(false)}
          placement='bottom'
          classNames={{
            wrapper: 'z-[200]'
            // base: 'm-0 rounded-b-none',
            // body: 'p-3'
          }}
        >
          <DrawerContent>
            <DrawerHeader className='flex items-center justify-between'>
              <span className='font-semibold'>
                {label} {selected.length > 0 && <span className='text-default-600'>({selected.length})</span>}
              </span>
              <div className='flex gap-1'>
                {selected.length > 0 && (
                  // <Button isIconOnly size='sm' radius='sm' variant='light' onPress={clearSelected}>
                  //   <DeleteIcon className='h-4 w-4' />
                  // </Button>
                  <Button size='sm' radius='sm' variant='light' color='danger' onPress={clearSelected} startContent={<DeleteIcon className='h-4 w-4' />}>
                    Очистить
                  </Button>
                )}
              </div>
            </DrawerHeader>
            <DrawerBody>{FilterContent}</DrawerBody>
            <DrawerFooter>
              <Button fullWidth color='warning' onPress={applyFilters}>
                Применить
              </Button>
            </DrawerFooter>
          </DrawerContent>
        </Drawer>
      </>
    )
  }

  return (
    <div className='space-y-3'>
      <div className='flex items-center justify-between'>
        <h3 className='text-sm font-semibold'>
          {label} {selected.length > 0 && <span className='text-default-600'>({selected.length})</span>}
        </h3>
        {selected.length > 0 && (
          <Button isIconOnly size='sm' radius='sm' color='danger' variant='light' onPress={clearSelected}>
            <DeleteIcon className='h-5 w-5' />
          </Button>
        )}
      </div>
      {FilterContent}
    </div>
  )
}
