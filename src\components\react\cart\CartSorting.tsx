import { Select, SelectItem } from '@heroui/react'
import { navigate } from 'astro:transitions/client'
import { ArrowDownUpIcon } from 'lucide-react'

const sortingOptions = [
  { value: '', label: 'По дате' },
  { value: 'prod_size', label: 'По размеру' },
  { value: 'prod_type', label: 'По типу' },
  { value: 'prod_manuf', label: 'По бренду' }
]

export const CartSorting = ({ initialSorting, isMobile }: { initialSorting?: string, isMobile: boolean }) => {
  const handleSortingChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const newSorting = event.target.value
    const url = new URL(window.location.href)
    if (newSorting) {
      url.searchParams.set('sorting', newSorting)
    } else {
      url.searchParams.delete('sorting')
    }
    navigate(url.pathname + url.search)
  }

  const currentSorting = initialSorting || ''

  return (
    <div className='flex items-center gap-2'>
      <Select
        startContent={<ArrowDownUpIcon className='w-5 h-5'/>}
        variant='bordered'
        size={isMobile ? 'sm' : 'md'}
        selectedKeys={[currentSorting]}
        onSelectionChange={(keys) => {
          const newSorting = Array.from(keys)[0] as string
          const url = new URL(window.location.href)
          if (newSorting) {
            url.searchParams.set('sorting', newSorting)
          } else {
            url.searchParams.delete('sorting')
          }
          navigate(url.pathname + url.search)
        }}
        className='w-48'
        aria-label='Сортировка товаров в корзине'
      >
        {sortingOptions.map((option) => (
          <SelectItem key={option.value}>{option.label}</SelectItem>
        ))}
      </Select>
    </div>
  )
} 