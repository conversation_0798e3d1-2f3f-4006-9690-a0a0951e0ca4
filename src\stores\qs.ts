import { atom } from 'nanostores'
import { navigate } from 'astro:transitions/client'
import queryString from 'query-string'

interface URLParamsState {
  page?: number
  limit?: number
  sortBy?: string
  order?: 'desc' | 'asc'
  sorting?: any[]
  search?: string
  filters?: any[]
}

const stores = new Map()

export const currentPageStore = atom(1)

function getCurrentPageId() {
  if (typeof window === 'undefined') return null
  return window.location.pathname
}

function deepMerge(target: any, source: any) {
  for (const key in source) {
    if (source[key] instanceof Object && !Array.isArray(source[key])) {
      target[key] = target[key] || {}
      deepMerge(target[key], source[key])
    } else {
      target[key] = source[key]
    }
  }
  return target
}


export function updateURLParams(updates: Partial<URLParamsState>, pageId?: string, doNotNavigate = false) {
  // Если обновляются фильтры, то сбрасываем страницу на первую
  if (updates.filters) {
    updates.page = 1
    currentPageStore.set(1)
  }

  const currentPageId = pageId || getCurrentPageId()
  let store = stores.get(currentPageId)

  // Обновляем стор страницы при изменении page
  if (updates.page) {
    currentPageStore.set(updates.page)
  }

  if (!store) {
    const initialState = typeof window !== 'undefined' ? queryString.parse(window.location.search) : {}
    store = atom(initialState)
    stores.set(currentPageId, store)
  }

  const currentState = store.get()

  // Безопасный парсинг с сохранением оригинального значения
  const parsedCurrentState = Object.entries(currentState).reduce((acc, [key, value]) => {
    try {
      acc[key] = typeof value === 'string' ? JSON.parse(value) : value
    } catch {
      acc[key] = value
    }
    return acc
  }, {})

  // const newState = {
  //   ...parsedCurrentState, // Используем parsed состояние
  //   ...updates
  // }

  const newState = deepMerge({ ...parsedCurrentState }, updates)

  const stringifiedState = Object.entries(newState).reduce((acc, [key, value]) => {
    acc[key] = typeof value === 'object' && value !== null ? JSON.stringify(value) : value
    return acc
  }, {})

  store.set(stringifiedState)

  if (typeof window !== 'undefined') {
    if (!doNotNavigate) {
      navigate(window.location.origin + window.location.pathname + '?' + queryString.stringify(stringifiedState))
    } else {
      const newUrl = window.location.pathname + '?' + queryString.stringify(stringifiedState)
      window.history.pushState({}, '', newUrl)
    }
  }

  return queryString.stringify(stringifiedState)
}

export function getStore(pageId?: string) {
  const currentPageId = pageId || getCurrentPageId()
  return stores.get(currentPageId)
}

// Хелпер для получения текущей страницы
export function getCurrentPage() {
  return currentPageStore.get()
}

// Удаляем хелпер setCurrentPage, так как он создает дополнительный цикл обновлений
// export function setCurrentPage(page: number) {
//   currentPageStore.set(page)
//   return updateURLParams({ page }, undefined, false)
// }

export function clearStoreState(pageId?: string) {
  const currentPageId = pageId || getCurrentPageId()
  let store = stores.get(currentPageId)

  if (store) {
    store.set({})  // очищаем состояние
  }

  currentPageStore.set(1)  // сбрасываем текущую страницу на первую
}
