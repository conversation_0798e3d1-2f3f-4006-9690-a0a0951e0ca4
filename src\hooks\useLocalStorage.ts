import { useState, useEffect } from 'react'

const isClient = typeof window !== 'undefined'

const safeLocalStorage = {
  getItem(key: string) {
    if (!isClient) return null
    try {
      return window.localStorage.getItem(key)
    } catch (e) {
      console.error('Error accessing localStorage:', e)
      return null
    }
  },
  setItem(key: string, value: string) {
    if (!isClient) return
    try {
      window.localStorage.setItem(key, value)
    } catch (e) {
      console.error('Error accessing localStorage:', e)
    }
  },
  removeItem(key: string) {
    if (!isClient) return
    try {
      window.localStorage.removeItem(key)
    } catch (e) {
      console.error('Error accessing localStorage:', e)
    }
  }
}

interface UseLocalStorageOptions {
  skipStorage?: boolean
}

export function useLocalStorage<T>(
  key: string,
  initialValue: T,
  options: UseLocalStorageOptions = {}
): [T, (value: T | ((val: T) => T)) => void] {
  const [storedValue, setStoredValue] = useState<T>(() => {
    if (typeof window === 'undefined') return initialValue
    
    try {
      const item = window.localStorage.getItem(key)
      return item ? JSON.parse(item) : initialValue
    } catch (error) {
      console.error(error)
      return initialValue
    }
  })

  const setValue = (value: T | ((val: T) => T)) => {
    try {
      const valueToStore = value instanceof Function ? value(storedValue) : value
      setStoredValue(valueToStore)

      if (!options.skipStorage && typeof window !== 'undefined') {
        window.localStorage.setItem(key, JSON.stringify(valueToStore))
      }
    } catch (error) {
      console.error(error)
    }
  }

  return [storedValue, setValue]
}
