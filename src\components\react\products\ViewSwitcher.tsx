import { Tab, Tabs } from '@heroui/react'
import { LayoutGridIcon, TableIcon } from 'lucide-react'
import { useStore } from '@nanostores/react'
import { viewModeStore, type ViewMode } from '@/stores/viewMode'

interface Props {
  onSelect?: (key: ViewMode) => void
}

export const ViewSwitcher = ({ onSelect }: Props) => {
  const currentView = useStore(viewModeStore)

  const handleSelect = (key: ViewMode) => {
    document.cookie = `view-mode=${key};path=/;max-age=31536000;SameSite=Strict`
    viewModeStore.set(key)
    onSelect?.(key)

    window.location.reload()
  }

  return (
    <Tabs
      selectedKey={currentView}
      onSelectionChange={(k) => handleSelect(k as ViewMode)}
      variant='bordered'
      aria-label="Переключение режима отображения товаров"
    >
      <Tab
        title={
          <div className='flex items-center gap-2'>
            <span>Сетка</span> <LayoutGridIcon className='w-4' aria-hidden="true" />
          </div>
        }
        key='grid'
        aria-label="Отображение товаров сеткой"
      />
      <Tab
        title={
          <div className='flex items-center gap-2'>
            <span>Таблица</span> <TableIcon className='w-4' aria-hidden="true" />
          </div>
        }
        key='table'
        aria-label="Отображение товаров таблицей"
      />
    </Tabs>
  )
}
