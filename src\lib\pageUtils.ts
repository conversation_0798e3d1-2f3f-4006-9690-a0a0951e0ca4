import { defaultAliases } from '@/types/aliases'
import type { URLSearchParams } from 'url'

export interface PageParams {
  limit?: number
  page?: number
  order?: string
  sorting?: any
  filters?: any
  search?: string
}

export function getPageParams(searchParams: URLSearchParams): PageParams {
  return {
    limit: searchParams.has('limit') ? Number(searchParams.get('limit')) : 50,
    page: searchParams.has('page') ? Number(searchParams.get('page')) : 1,
    order: searchParams.get('order') || 'desc',
    sorting: getInitialSorting(searchParams),
    filters: searchParams.get('filters') || null,
    search: searchParams.get('search') || undefined
  }
}

export function getInitialSorting(searchParams: URLSearchParams) {
  const sortingParam = searchParams.get('sorting')
  if (!sortingParam) {
    return undefined
  }

  try {
    // Попытка распарсить как JSON для сложной сортировки
    return JSON.parse(sortingParam)
  } catch (error) {
    // Если не JSON, считаем это простой строкой и формируем объект
    return [{ column: sortingParam, direction: 'asc' }]
  }
}

export function getInitialFilterValue(searchParams: URLSearchParams) {
  const filtersParam = searchParams.get('filters')
  if (!filtersParam) {
    return {
      filtersValue: null,
      filtersStr: ''
    }
  }

  try {
    const filters = JSON.parse(filtersParam)
    // Обрабатываем значения как массив для prod_cat
    const filtersStr = Object.entries(filters)
      .map(([key, value]) => {
        if (key === 'prod_cat') {
          // Для prod_cat обрабатываем массив значений
          return Array.isArray(value)
            ? value.map((v) => (defaultAliases.prod_cat as Record<string, string>)?.[v] || v).join(', ')
            : value
        }

        if (key === 'inStock') {
          return value ? 'В наличии' : ''
        }

        // Для остальных полей оставляем текущую логику
        return Array.isArray(value)
          ? value.map((v) => (defaultAliases as Record<string, Record<string, string>>)?.[key]?.[v] || v).join(', ')
          : value
      })
      .filter(Boolean)
      .join(', ')

    return {
      filtersValue: filters,
      filtersStr
    }
  } catch (error) {
    return {
      filtersValue: null,
      filtersStr: ''
    }
  }
}

export function getPageFiltersAndSort(searchParams: URLSearchParams) {
  const { limit, page, order, sorting, filters, search } = getPageParams(searchParams)
  const { filtersValue, filtersStr } = getInitialFilterValue(searchParams)

  return {
    params: {
      limit,
      page,
      order,
      sorting,
      filters,
      search
    },
    filters: {
      filtersValue,
      filtersStr
    }
  }
}
