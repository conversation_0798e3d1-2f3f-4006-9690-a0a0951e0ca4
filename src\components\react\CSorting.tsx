import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>, <PERSON>er<PERSON>ooter, Listbox, ListboxItem } from '@heroui/react'
import { useState, useEffect, useCallback, useMemo } from 'react'
import { updateURLParams } from '@/stores/qs'
import { ArrowUpNarrowWide, ArrowDownNarrowWide, ArrowUpDownIcon, ArrowDownWideNarrowIcon } from 'lucide-react'
import type { Category } from '@/types/GetCategoryProduct'

interface Props {
  initialSorting?: {
    column: string
    direction: 'asc' | 'desc'
    order?: 1 | 0
  }[]
  options?: Array<{ value: string; label: string; icon?: React.ReactNode }>
  isMobile?: boolean
  columns?: Category['columns']
  label?: string
  size?: 'sm' | 'md' | 'lg'
  variant?: 'flat' | 'solid' | 'bordered' | 'light' | 'faded' | 'shadow' | 'ghost' | undefined
}

const generateSortOptions = (columns?: Category['columns']) => {
  if (!columns?.length) {
    return [
      {
        value: 'prod_price:asc',
        label: 'Цена',
        icon: <ArrowUpNarrowWide className='w-4' strokeWidth='1' />
      },
      {
        value: 'prod_price:desc',
        label: 'Цена',
        icon: <ArrowDownNarrowWide className='w-4' strokeWidth='1' />
      },
      {
        value: 'prod_count:asc',
        label: 'Наличие',
        icon: <ArrowUpNarrowWide className='w-4' strokeWidth='1' />
      },
      {
        value: 'prod_count:desc',
        label: 'Наличие',
        icon: <ArrowDownNarrowWide className='w-4' strokeWidth='1' />
      }
    ]
  }

  const uniqueColumns = columns.flat().filter((col, index, self) => index === self.findIndex((c) => c.keyname === col.keyname))

  return uniqueColumns.flatMap((col) => [
    {
      value: `${col.keyname}:asc`,
      label: col.title,
      icon: <ArrowUpNarrowWide className='w-4' strokeWidth='2' />
    },
    {
      value: `${col.keyname}:desc`,
      label: col.title,
      icon: <ArrowDownWideNarrowIcon className='w-4' strokeWidth='2' />
    }
  ])
}

export const CSorting = ({ columns, initialSorting = [], options, isMobile = false, label, size = 'md', variant = 'bordered' }: Props) => {
  const [isOpen, setIsOpen] = useState(false)
  // const [selected, setSelected] = useState('')
  const sortOptions = generateSortOptions(columns)
  const mergedOptions = options || sortOptions

  const [selected, setSelected] = useState(new Set(initialSorting?.length ? initialSorting.map((i) => `${i.column}:${i.direction}`) : ['']))

  const selectedValue = useMemo(() => Array.from(selected)[0], [selected])

  // if (initialSorting?.length) {
  //   setSelected(new Set(initialSorting.map((i) => `${i.column}:${i.direction}`)))
  // }

  useEffect(() => {
    //console.log('selected:', selected)
  }, [selected])

  useEffect(() => {
    //console.log('selectedValue:', selectedValue)
  }, [selectedValue])

  const selectedIcon = useCallback(() => {
    // const [field, direction] = selectedValue.split(':')
    const f = mergedOptions.find((x) => x.value === selectedValue)
    //console.log('selectedIcon: ', { selectedValue, f })

    return f?.icon
  }, [selected, mergedOptions])

  const selectedTitle = useCallback(() => {
    const f = mergedOptions.find((x) => x.value === selectedValue)
    return f?.label
  }, [selected, mergedOptions])

  // useEffect(() => {
  //   const urlParams = new URLSearchParams(window.location.search)
  //   const sortingParam = urlParams.get('sorting')
  //   const sorting = sortingParam ? JSON.parse(sortingParam) : initialSorting
  //   const newSelected = sorting[0] ? `${sorting[0].column}:${sorting[0].direction}` : ''

  //   if (newSelected && !mergedOptions.some((opt) => opt.value === newSelected)) {
  //     setSelected('')
  //     return
  //   }

  //   setSelected(newSelected)
  // }, [initialSorting, mergedOptions])

  const handleApply = () => {
    setIsOpen(false)
    const [column, direction] = selectedValue.split(':')
    const sorting = selected ? [{ column, direction, order: 1 }] : []
    updateURLParams({ sorting: JSON.stringify(sorting) })
  }

  return (
    <>
      <Button
        size={size}
        onPress={() => setIsOpen(true)}
        isIconOnly={!selectedValue && !label}
        variant={variant}
        aria-label={!selectedValue ? 'Открыть меню сортировки' : `Сортировка: ${selectedTitle()}`}
        aria-haspopup="dialog"
      >
        {!selectedValue && (
          <span className='flex items-center'>
            {label && <span>{label}</span>}
            <ArrowUpDownIcon className='w-4 text-default-900' strokeWidth={1} />
          </span>
        )}
        <div className='flex items-center gap-1'>
          {selectedTitle()}
          {selectedIcon()}
        </div>
      </Button>

      <Drawer
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        placement={isMobile ? 'bottom' : 'right'}
        scrollBehavior='inside'
        aria-labelledby="sort-drawer-header"
        classNames={{
          wrapper: 'z-[200]',
          base: 'm-0 rounded-b-none sm:rounded-b-lg',
          body: 'p-3'
        }}
      >
        <DrawerContent>
          <DrawerHeader id="sort-drawer-header" className='font-semibold'>Выбор сортировки</DrawerHeader>
          <DrawerBody>
            <Listbox
              // disallowEmptySelection
              aria-label='Выбор сортировки'
              selectedKeys={selected}
              selectionMode='single'
              variant='flat'
              onSelectionChange={setSelected}
            >
              {mergedOptions.map((opt, index) => {
                const [_, direction] = opt.value.split(':')
                return (
                  <ListboxItem
                    textValue={`${opt.label} ${direction === 'asc' ? 'По возрастанию' : 'По убыванию'}`}
                    showDivider={index % 2 == 1}
                    key={opt.value}
                    value={opt.value}
                    endContent={opt.icon}
                  >
                    <span className='font-semibold'>{opt.label}</span>{' '}
                    <span className='text-xs'>({direction === 'asc' ? 'По возрастанию' : 'По убыванию'})</span>
                  </ListboxItem>
                )
              })}
            </Listbox>
            {/* <div className='space-y-2'>
              {mergedOptions.map((opt) => {
                const [_, direction] = opt.value.split(':')
                return (
                  <Button
                    key={opt.value}
                    fullWidth
                    variant={selected === opt.value ? 'solid' : 'light'}
                    color={selected === opt.value ? 'warning' : 'default'}
                    className='justify-start'
                    onPress={() => setSelected(opt.value)}
                  >
                    {opt.label} ({direction === 'asc' ? 'По возрастанию' : 'По убыванию'})
                  </Button>
                )
              })}
            </div> */}
          </DrawerBody>
          <DrawerFooter>
            <Button
              fullWidth={isMobile}
              color='warning'
              onPress={handleApply}
              aria-label="Применить выбранную сортировку"
            >
              Применить
            </Button>
          </DrawerFooter>
        </DrawerContent>
      </Drawer>
    </>
  )
}
