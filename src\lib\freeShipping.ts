import { useStore } from '@tanstack/react-store'
import { cartSums } from '@/stores/cart'
import { appSettingsStore } from '@/stores/settings'
import type { AppSettings } from '@/types/AppSettings'

export interface FreeShippingConfig {
  isActive: boolean
  startSum: number
  introText: string
}

export interface FreeShippingInfo {
  isEligible: boolean
  remainingAmount: number
  config: FreeShippingConfig | null
}

export const useFreeShipping = (): FreeShippingInfo => {
  const $cartSums = useStore(cartSums)
  const settings = appSettingsStore.state as Partial<AppSettings> | undefined

  const config: FreeShippingConfig | null = settings
    ? {
        isActive: settings['free_shipping.isactive'] === 1,
        startSum: settings['free_shipping.startsum'] || 0,
        introText: settings['free_shipping.intro_text'] || '',
      }
    : null

  const currentSum = $cartSums?.sum || 0
  const isEligible = Boolean(config?.isActive && currentSum >= (config?.startSum || 0))
  const remainingAmount = config?.isActive ? Math.max(0, (config?.startSum || 0) - currentSum) : 0

  return {
    isEligible,
    remainingAmount,
    config,
  }
}

export const getFreeShippingConfig = (): FreeShippingConfig | null => {
  const settings = appSettingsStore.state as Partial<AppSettings> | undefined

  if (!settings) return null

  return {
    isActive: settings['free_shipping.isactive'] === 1,
    startSum: settings['free_shipping.startsum'] || 0,
    introText: settings['free_shipping.intro_text'] || '',
  }
}