import { Dropdown, DropdownTrigger, DropdownMenu, DropdownItem, Button } from '@heroui/react'
import { MenuIcon } from 'lucide-react'

export const NavbarDropdownMenu = () => {
  const items = [
    {
      key: 'home',
      label: 'Главная',
      link: '/'
    },
    {
      key: 'payment',
      label: 'Оплата',
      link: '/payment'
    },
    {
      key: 'shipping',
      label: 'Доставка',
      link: '/shipping'
    },
    {
      key: 'wholesale',
      label: 'Оптовикам',
      link: '/price'
    },
    {
      key: 'qa',
      label: 'Вопрос-ответ',
      link: '/qa'
    },
    {
      key: 'contacts',
      label: 'Контакты',
      link: '/contacts'
    }
  ]

  return (
    <Dropdown size='lg' shadow='lg'>
      <DropdownTrigger>
        <Button className='bg-default-500' isIconOnly size='lg' variant='flat'>
          <MenuIcon className=' text-white'/>
        </Button>
      </DropdownTrigger>
      <DropdownMenu aria-label='Основное меню' items={items}>
        {(item) => (
          <DropdownItem key={item.key} href={item.link}>
            {item.label}
          </DropdownItem>
        )}
      </DropdownMenu>
    </Dropdown>
  )
}
