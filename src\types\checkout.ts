export interface OrderRecipient {
  firstName: string
  lastName: string
  phone: string
  isLegalEntity: boolean
  companyName: string
  inn: string
  kpp: string
  ogrn: string
  bank?: string
  bik?: string
  rschet?: string
  kschet?: string
  fullName: string
  email: string
}

export interface OrderAddress {
  country: string
  countryData: {
    iso: number
    country_id: number
  }
  location: string
  locationData: unknown
  street: string
  house: string
  apartment: string
  zipCode: string
}

export interface OrderData {
  recipient: OrderRecipient
  address: OrderAddress
  payment: {
    method: string
  }
  shipping: {
    method: string
    price: number
    notice: string
  }
  notice?: string
}

export interface FormsValidity {
  recipient: boolean
  address: boolean
  shipping: boolean
  payment: boolean
}
