import { memo, useState, useCallback } from 'react'
import { useStore } from '@nanostores/react'
import { currentPageStore, updateURLParams } from '@/stores/qs'
import { ProductList } from "./ProductList"
import { Button } from '@heroui/react'
import { trpc } from '@/trpc'

const List = memo(({ data, ...props }) => (
  <ProductList data={data} {...props} />
))

export const SearchProductViewer = ({ data: initialData, searchvalue, ...props }) => {
  const [items, setItems] = useState(initialData)
  const [isLoading, setIsLoading] = useState(false)
  const currentPage = useStore(currentPageStore)

  // Рендерим все категории сразу
  const renderCategories = () => {
    return items.categories.map((categoryData, index) => (
      <div key={categoryData.categoryId} className='mb-5 rounded-lg border p-2 dark:border-default-200'>
        <h2 className='pb-2 text-base'>
          Найдено в: <span className="font-semibold">{categoryData.categoryTitle || ''}</span>
        </h2>
        
        <List
          showCounter={index + 1}
          data={{
            columns: categoryData.columns,
            data: categoryData.products
          }}
          {...props}
        />
      </div>
    ))
  }

  const loadMore = useCallback(async () => {
    if (isLoading) return
    setIsLoading(true)

    try {
      // Запрашиваем все страницы от 1 до следующей
      const nextPage = currentPage + 1
      const promises = Array.from({ length: nextPage }, (_, i) => 
        trpc.products.globalSearchMeili.query({
          searchvalue,
          limit: props.limit,
          page: i + 1,
          order: props.order,
          filters: props.filters
        })
      )

      const results = await Promise.all(promises)
      
      // Объединяем все результаты
      const mergedData = results.reduce((acc, pageData) => {
        pageData.categories.forEach((newCategory) => {
          const existingCategory = acc.categories.find(c => c.categoryId === newCategory.categoryId)
          if (existingCategory) {
            // Добавляем только уникальные продукты
            const existingIds = new Set(existingCategory.products.map(p => p.prod_id))
            const uniqueNewProducts = newCategory.products.filter(p => !existingIds.has(p.prod_id))
            existingCategory.products.push(...uniqueNewProducts)
          } else {
            acc.categories.push({ ...newCategory })
          }
        })
        return acc
      }, { categories: [], meta: results[results.length - 1].meta })

      setItems(mergedData)
      updateURLParams({ page: nextPage }, undefined, true)
      currentPageStore.set(nextPage)
    } finally {
      setIsLoading(false)
    }
  }, [currentPage, searchvalue, props.limit, props.order, props.filters])

  const hasMore = currentPage < (items.meta?.pageCount || 1)

  return (
    <div>
      {renderCategories()}
      
      {hasMore && (
        <div className="flex flex-col items-center gap-2 mt-4">
          <Button
            onPress={loadMore}
            isLoading={isLoading}
          >
            {isLoading ? 'Загрузка...' : `Показать еще`}
          </Button>
        </div>
      )}
    </div>
  )
}
