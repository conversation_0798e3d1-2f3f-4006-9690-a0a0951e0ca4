---
import { trpc } from '@/trpc'
import { SchemaViewer } from '@components/react/SchemaViewer'

const { product } = Astro.props

const schema = await trpc.products.getProductSchemaByProductId.query(product?.prod_id)
const isMobile = Astro.locals.isMobile
---

{
  product?.prod_id && schema && (
    <div>
      <SchemaViewer isMobile={isMobile} client:only='react' data={schema} product={product} >
        {/* <div slot="fallback">
            Загрузка схемы
        </div> */}
      </SchemaViewer>
    </div>
  )
}
