---
import { SearchAIChat } from '@/components/react/SearchAIChat'
import Layout from '@layouts/Layout.astro'
const searchvalue = Astro.url.searchParams.get('searchvalue')
---

<Layout title='Поиск с ИИ'>
  <div class='container mx-auto p-3'>
    <SearchAIChat clientInitMessage={searchvalue} server:defer client:only='react' />
  </div>

  <style>
    .aimessage a:hover {
      text-decoration: underline;
    }
  </style>
</Layout>
