/* purgecss start ignore */
@tailwind base;
@tailwind components;
@tailwind utilities;
/* purgecss end ignore */

/* @import "tailwindcss"; */

@layer base {
  :root {
    --radius: 0.5rem;
  }

  /* Убираем стандартную обводку при фокусе со всех элементов */
  *:focus {
    outline: none !important;
  }

  /* Убираем обводку с инпутов */
  input:focus,
  textarea:focus,
  select:focus,
  button:focus,
  [role="button"]:focus,
  a:focus,
  [tabindex]:focus {
    outline: none !important;
    box-shadow: none !important;
  }

  /* Базовые стили для скрытия стрелок в input type="number" */
  input[type="number"] {
    -webkit-appearance: none !important;
    -moz-appearance: textfield !important;
    appearance: textfield !important;
    margin: 0 !important;
  }

  /* Скрываем стрелки в input type="number" для Chrome, Safari, Edge, Opera */
  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
    appearance: none !important;
    margin: 0 !important;
  }

  /* Firefox-specific стили */
  @-moz-document regexp('.*') {
    input[type="number"] {
      -moz-appearance: textfield !important;
      appearance: textfield !important;
    }
  }

  /* Дополнительный подход для Firefox */
  @supports (-moz-appearance:none) and (display:grid) {
    input[type="number"] {
      -moz-appearance: textfield !important;
      appearance: textfield !important;
    }
  }
}

body {
  /* @apply dark:bg-default-50 !important; */
}

@media (max-width: 500px) {
  .schema-container {
    transform: scale(0.5) translate(-200px);
  }
}

.p-card {
  @apply rounded-sm bg-white p-3 dark:bg-zinc-900;
}

.ais-highlight {
  background-color: yellow;
  font-style: italic;
}

/* Пример CSS-стилей для подсветки элемента схемы */
.active-highlight {
  stroke: #f6bb3b !important; /* изменяет цвет обводки */
  filter: drop-shadow(0 0 5px #f6bb3b);
}

.button {
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000;
}

.embla {
  max-width: 48rem;
  margin: auto;
  --slide-height: 19rem;
  --slide-spacing: 1rem;
  --slide-size: 70%;
}
.embla__viewport {
  overflow: hidden;
}
.embla__container {
  display: flex;
  touch-action: pan-y pinch-zoom;
  margin-left: calc(var(--slide-spacing) * -1);
}
.embla__slide {
  transform: translate3d(0, 0, 0);
  flex: 0 0 var(--slide-size);
  min-width: 0;
  padding-left: var(--slide-spacing);
}
.embla__slide__img {
  border-radius: 1.8rem;
  display: block;
  height: var(--slide-height);
  width: 100%;
  object-fit: cover;
}
.embla__controls {
  display: grid;
  grid-template-columns: auto 1fr;
  justify-content: space-between;
  gap: 1.2rem;
  margin-top: 1.8rem;
}
.embla__buttons {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.6rem;
  align-items: center;
}
.embla__button {
  -webkit-tap-highlight-color: rgba(var(--text-high-contrast-rgb-value), 0.5);
  -webkit-appearance: none;
  appearance: none;
  background-color: transparent;
  touch-action: manipulation;
  display: inline-flex;
  text-decoration: none;
  cursor: pointer;
  border: 0;
  padding: 0;
  margin: 0;
  box-shadow: inset 0 0 0 0.2rem var(--detail-medium-contrast);
  width: 3.6rem;
  height: 3.6rem;
  z-index: 1;
  border-radius: 50%;
  color: var(--text-body);
  display: flex;
  align-items: center;
  justify-content: center;
}
.embla__button:disabled {
  color: var(--detail-high-contrast);
}
.embla__button__svg {
  width: 35%;
  height: 35%;
}
.embla__dots {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-end;
  align-items: center;
  margin-right: calc((2.6rem - 1.4rem) / 2 * -1);
}
.embla__dot {
  -webkit-tap-highlight-color: rgba(var(--text-high-contrast-rgb-value), 0.5);
  -webkit-appearance: none;
  appearance: none;
  background-color: transparent;
  touch-action: manipulation;
  display: inline-flex;
  text-decoration: none;
  cursor: pointer;
  border: 0;
  padding: 0;
  margin: 0;
  width: 2.6rem;
  height: 2.6rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}
.embla__dot:after {
  box-shadow: inset 0 0 0 0.2rem var(--detail-medium-contrast);
  width: 1.4rem;
  height: 1.4rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  content: '';
}
.embla__dot--selected:after {
  box-shadow: inset 0 0 0 0.2rem var(--text-body);
}
