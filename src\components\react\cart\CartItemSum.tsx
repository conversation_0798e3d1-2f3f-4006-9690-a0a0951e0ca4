import { memo } from 'react'
import { cartItems, getCartItem, whosalePrices } from '@/stores/cart'
import type { CategoryProduct } from '@/types/GetCategoryProduct'
import { priceFormat } from '@/lib/priceFormat'
import { useStore } from '@tanstack/react-store'

interface Props {
  product: CategoryProduct
  wholesalePrice?: boolean
}

export const CartItemSum = ({ product, wholesalePrice }: Props) => {
  const $cartItem = useStore(cartItems)
  const $whosalePrices = useStore(whosalePrices)

  const cartItem = $cartItem[product.prod_id]
  const qty = cartItem?.qty || 0

  // Выбираем цену в зависимости от типа цены
  const price = wholesalePrice || $whosalePrices ? product.whosaleprice : product.prod_price
  const sum = (price || 0) * qty

  //console.log('sum:', sum)

  return <span>{priceFormat(sum)}</span>
}
