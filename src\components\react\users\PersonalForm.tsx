import { Input, Switch, Form, Button } from '@heroui/react'
import { Autocomplete, AutocompleteItem } from '@heroui/react'
import { useEffect, useState } from 'react'
import { useForm, Controller } from 'react-hook-form'

interface PersonalFormProps {
  data: {
    fullName: string
    email: string
    phone: string
    isLegalEntity: boolean
    companyName: string
    inn: string
    kpp: string
    ogrn: string
    address: string // Добавляем юр.адрес
    bank: string
    bik: string
    rschet: string
    kschet: string
  }
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void
  onToggleLegalEntity: (value: boolean) => void
  handleNext: () => void
  onValidityChange?: (isValid: boolean) => void
}

export const PersonalForm = ({ onValidityChange, data: initialData, onChange, onToggleLegalEntity, handleNext, nextBtnTitle, showNextPermanent = false }) => {
  const [orgSuggestions, setOrgSuggestions] = useState<[]>([])
  const [bankSuggestions, setBankSuggestions] = useState<[]>([])
  const [selectedOrg, setSelectedOrg] = useState<null>(null)
  const [selectedBank, setSelectedBank] = useState<null>(null)

  const [showNextBtn, setShowNextBtn] = useState(true)

  const { control, handleSubmit, watch, setValue, trigger, formState } = useForm({
    defaultValues: initialData,
    mode: 'onChange',
    reValidateMode: 'onChange',
    values: initialData // Устанавливаем текущие значения
  })

  const isLegalEntity = watch('isLegalEntity')

  useEffect(() => {
    //console.log('PersonalForm - initialData changed:', initialData)
  }, [initialData])

  useEffect(() => {
    // Обновляем только если значения действительно изменились
    Object.entries(initialData).forEach(([key, value]) => {
      const currentValue = watch(key)
      if (currentValue !== value) {
        setValue(key, value)
      }
    })
  }, [initialData, setValue, watch])

  const onSubmit = (data) => {
    //console.log('PersonalForm - onSubmit:', data)
    // Check if onChange handler is called
    Object.entries(data).forEach(([key, value]) => {
      const event = {
        target: { name: key, value }
      } as React.ChangeEvent<HTMLInputElement>
      onChange(event)
    })
    handleNext?.()
    !showNextPermanent && setShowNextBtn(false)
  }

  const handleFieldChange = (name: string, value: any) => {
    //console.log('PersonalForm - field changed:', name, value)
    onChange({ target: { name, value } } as React.ChangeEvent<HTMLInputElement>)
  }

  const handleOrgSearch = async (query: string) => {
    if (!query || query.length < 2) {
      setOrgSuggestions([])
      return
    }

    try {
      const response = await fetch(`/api/service/findorg/${query}`, {
        headers: { 'Content-Type': 'application/json' }
      })
      if (!response.ok) throw new Error('Ошибка поиска организации')
      const items = await response.json()
      setOrgSuggestions(items)
    } catch (error) {
      console.error('Ошибка при поиске организации:', error)
      setOrgSuggestions([])
    }
  }

  const handleBankSearch = async (query: string) => {
    if (!query || query.length < 2) {
      setBankSuggestions([])
      return
    }

    try {
      const response = await fetch(`/api/service/findbank/${query}`, {
        headers: { 'Content-Type': 'application/json' }
      })
      if (!response.ok) throw new Error('Ошибка поиска банка')
      const items = await response.json()
      setBankSuggestions(items)
    } catch (error) {
      console.error('Ошибка при поиске банка:', error)
      setBankSuggestions([])
    }
  }

  const handleOrgSelect = (org) => {
    //console.log('PersonalForm handleOrgSelect - start:', { org, currentFormValues: watch() })

    setSelectedOrg(org)
    if (org) {
      // Собираем все данные организации сразу
      const updates = {
        companyName: org.value,
        inn: org.data.inn,
        kpp: org.data.kpp,
        ogrn: org.data.ogrn || '',
        address: org?.data?.address?.unrestricted_value || org?.data?.address?.value || ''
      }

      //console.log('PersonalForm - org updates:', updates)

      // Сначала устанавливаем значения в форму
      Object.entries(updates).forEach(([key, value]) => {
        setValue(key, value, { shouldDirty: true })
      })

      // Затем синхронизируем с родителем одним вызовом
      const event = {
        target: {
          name: 'orgData',
          value: updates
        }
      } as React.ChangeEvent<HTMLInputElement>

      onChange(event)
    }
  }

  const handleBankSelect = (bank) => {
    //console.log('PersonalForm handleBankSelect - start:', { bank })

    setSelectedBank(bank)
    if (bank) {
      const updates = {
        bank: bank.value,
        bik: bank.data.bic,
        rschet: bank.data.account || '',
        kschet: bank.data.correspondent_account || ''
      }

      //console.log('PersonalForm - bank updates:', updates)

      // Обновляем форму
      Object.entries(updates).forEach(([key, value]) => {
        setValue(key, value, { shouldDirty: true })
      })

      // Синхронизируем с родителем одним вызовом
      const event = {
        target: {
          name: 'bankData',
          value: updates
        }
      } as React.ChangeEvent<HTMLInputElement>

      onChange(event)
    }
  }

  const handleLegalEntityToggle = (checked: boolean) => {
    setValue('isLegalEntity', checked)
    onToggleLegalEntity(checked)

    if (!checked) {
      const fieldsToReset = [
        'companyName',
        'inn',
        'kpp',
        'ogrn',
        'address', // юридический адрес
        'bank',
        'bik',
        'rschet',
        'kschet'
      ]

      //console.log('PersonalForm - resetting fields:', fieldsToReset)

      fieldsToReset.forEach((field) => {
        setValue(field, '')
        // умышленно не вызываем onChange для предотвращения лишнего обновления
      })
      setSelectedOrg(null)
      setSelectedBank(null)
    }
  }

  useEffect(() => {
    onValidityChange?.(formState.isValid)
  }, [formState.isValid])

  return (
    <Form onSubmit={handleSubmit(onSubmit)}>
      <div className='flex flex-wrap gap-4'>
        <Controller
          control={control}
          name='fullName'
          rules={{ required: 'ФИО обязательно для заполнения' }}
          render={({ field: { ref, name, value, onChange, onBlur }, fieldState: { invalid, error } }) => (
            <Input
              ref={ref}
              name={name}
              value={value}
              onChange={(e) => {
                onChange(e)
                handleFieldChange(name, e.target.value)
              }}
              onBlur={onBlur}
              label='ФИО'
              placeholder='Введите ваше полное имя'
              isRequired
              validationBehavior='aria'
              isInvalid={invalid}
              errorMessage={error?.message}
            />
          )}
        />

        <div className='flex gap-4'>
          <Controller
            control={control}
            name='email'
            rules={{
              required: 'Email обязателен для заполнения',
              pattern: {
                value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                message: 'Некорректный email адрес'
              }
            }}
            render={({ field: { ref, name, value, onChange, onBlur }, fieldState: { invalid, error } }) => (
              <Input
                ref={ref}
                name={name}
                value={value}
                onChange={(e) => {
                  onChange(e)
                  handleFieldChange(name, e.target.value)
                }}
                onBlur={onBlur}
                type='email'
                label='Email'
                placeholder='Введите email'
                isRequired
                validationBehavior='aria'
                isInvalid={invalid}
                errorMessage={error?.message}
              />
            )}
          />

          <Controller
            control={control}
            name='phone'
            rules={{ required: 'Телефон обязателен для заполнения' }}
            render={({ field: { ref, name, value, onChange, onBlur }, fieldState: { invalid, error } }) => (
              <Input
                ref={ref}
                name={name}
                value={value}
                onChange={(e) => {
                  onChange(e)
                  handleFieldChange(name, e.target.value)
                }}
                onBlur={onBlur}
                label='Телефон'
                placeholder='+7 (999) 999-99-99'
                isRequired
                validationBehavior='aria'
                isInvalid={invalid}
                errorMessage={error?.message}
              />
            )}
          />
        </div>

        <div className='w-full'>
          <Controller
            control={control}
            name='isLegalEntity'
            render={({ field: { value, onChange } }) => (
              <Switch isSelected={value} onValueChange={(checked) => handleLegalEntityToggle(checked)}>
                Юридическое лицо
              </Switch>
            )}
          />
        </div>

        {isLegalEntity && (
          <div className='mt-4 flex w-full flex-col gap-4'>
            <Controller
              control={control}
              name='companyName'
              rules={{ required: isLegalEntity ? 'Название организации обязательно' : false }}
              render={({ field: { ref, name, value, onChange, onBlur }, fieldState: { invalid, error, touched } }) => (
                <Autocomplete
                  ref={ref}
                  allowsCustomValue
                  label='Организация'
                  placeholder='Поиск по ИНН/наименованию'
                  showScrollIndicators
                  isRequired={isLegalEntity}
                  scrollShadowProps={{ isEnabled: false }}
                  items={orgSuggestions}
                  inputValue={value}
                  onInputChange={(value) => {
                    onChange(value)
                    handleOrgSearch(value)
                  }}
                  selectedKey={selectedOrg?.value}
                  onSelectionChange={(key) => {
                    const selected = orgSuggestions.find((org) => org.data.hid === key)
                    handleOrgSelect(selected || null)
                  }}
                  onClose={() => {
                    trigger('companyName')
                  }}
                  isInvalid={touched && invalid}
                  errorMessage={touched ? error?.message : ''}
                >
                  {(org) => (
                    <AutocompleteItem key={org.data.hid} textValue={org.value}>
                      <div className='flex flex-col gap-1'>
                        <span>{org.value}</span>
                        <span className='text-sm text-default-600'>ИНН: {org.data.inn}</span>
                      </div>
                    </AutocompleteItem>
                  )}
                </Autocomplete>
              )}
            />

            <Controller
              control={control}
              name='address'
              rules={{ required: isLegalEntity ? 'Юридический адрес обязателен' : false }}
              render={({ field: { ref, name, value, onChange, onBlur }, fieldState: { invalid, error } }) => (
                <Input
                  ref={ref}
                  name={name}
                  value={value}
                  onChange={(e) => {
                    onChange(e)
                    handleFieldChange(name, e.target.value)
                  }}
                  onBlur={onBlur}
                  label='Юридический адрес'
                  placeholder='Введите юридический адрес организации'
                  isRequired={isLegalEntity}
                  validationBehavior='aria'
                  isInvalid={invalid}
                  errorMessage={error?.message}
                />
              )}
            />

            <Controller
              control={control}
              name='inn'
              rules={{ required: isLegalEntity ? 'ИНН обязателен' : false }}
              render={({ field: { ref, name, value, onChange, onBlur }, fieldState: { invalid, error } }) => (
                <Input
                  ref={ref}
                  name={name}
                  value={value}
                  onChange={(e) => {
                    onChange(e)
                    handleFieldChange(name, e.target.value)
                  }}
                  onBlur={onBlur}
                  label='ИНН'
                  isRequired={isLegalEntity}
                  isReadOnly={!!selectedOrg}
                  validationBehavior='aria'
                  isInvalid={invalid}
                  errorMessage={error?.message}
                />
              )}
            />

            <Controller
              control={control}
              name='kpp'
              // rules={{ required: isLegalEntity ? 'КПП обязателен' : false }}
              render={({ field: { ref, name, value, onChange, onBlur }, fieldState: { invalid, error } }) => (
                <Input
                  ref={ref}
                  name={name}
                  value={value}
                  onChange={(e) => {
                    onChange(e)
                    handleFieldChange(name, e.target.value)
                  }}
                  onBlur={onBlur}
                  label='КПП'
                  // isRequired={isLegalEntity}
                  isReadOnly={!!selectedOrg}
                  validationBehavior='aria'
                  isInvalid={invalid}
                  errorMessage={error?.message}
                />
              )}
            />

            <Controller
              control={control}
              name='bank'
              rules={{ required: isLegalEntity ? 'Банк обязателен' : false }}
              render={({ field: { ref, name, value, onChange, onBlur }, fieldState: { invalid, error, touched } }) => (
                <Autocomplete
                  ref={ref}
                  allowsCustomValue
                  label='Банк'
                  placeholder='Поиск по БИК/наименованию'
                  showScrollIndicators
                  isRequired={isLegalEntity}
                  scrollShadowProps={{ isEnabled: false }}
                  items={bankSuggestions}
                  inputValue={value}
                  onInputChange={(value) => {
                    onChange(value)
                    handleBankSearch(value)
                  }}
                  selectedKey={selectedBank?.value}
                  onSelectionChange={(key) => {
                    const selected = bankSuggestions.find((bank) => bank.value === key)
                    handleBankSelect(selected || null)
                  }}
                  onClose={() => {
                    trigger('bank')
                  }}
                  isInvalid={touched && invalid}
                  errorMessage={touched ? error?.message : ''}
                >
                  {(bank) => (
                    <AutocompleteItem key={bank.value} textValue={bank.value}>
                      <div className='flex flex-col gap-1'>
                        <span>{bank.value}</span>
                        <span className='text-sm text-default-600'>БИК: {bank.data.bic}</span>
                      </div>
                    </AutocompleteItem>
                  )}
                </Autocomplete>
              )}
            />

            <Controller
              control={control}
              name='bik'
              rules={{ required: isLegalEntity ? 'БИК обязателен' : false }}
              render={({ field: { ref, name, value, onChange, onBlur }, fieldState: { invalid, error } }) => (
                <Input
                  ref={ref}
                  name={name}
                  value={value}
                  onChange={(e) => {
                    onChange(e)
                    handleFieldChange(name, e.target.value)
                  }}
                  onBlur={onBlur}
                  label='БИК'
                  isRequired={isLegalEntity}
                  isReadOnly={!!selectedBank}
                  validationBehavior='aria'
                  isInvalid={invalid}
                  errorMessage={error?.message}
                />
              )}
            />

            <Controller
              control={control}
              name='rschet'
              rules={{ 
                required: isLegalEntity ? 'Расчетный счет обязателен' : false,
                validate: (value) => {
                  if (!value) return true
                  return value.length === 20 || 'Расчетный счет должен содержать 20 цифр'
                },
                pattern: {
                  value: /^\d{0,20}$/,  // Изменено с /^\d{20}$/ чтобы разрешить ввод
                  message: 'Расчетный счет должен содержать 20 цифр'
                }
              }}
              render={({ field }) => (
                <Input
                  {...field}
                  label='Расчетный счет'
                  placeholder='Введите расчетный счет'
                  value={field.value || ''}
                  onChange={(e) => {
                    const value = e.target.value.replace(/\D/g, '').slice(0, 20)
                    field.onChange(value)
                    handleFieldChange('rschet', value)
                  }}
                  isRequired={isLegalEntity}
                  validationBehavior='aria'
                />
              )}
            />

            <Controller
              control={control}
              name='kschet'
              rules={{ 
                required: isLegalEntity ? 'Корреспондентский счет обязателен' : false,
                pattern: {
                  value: /^\d{0,20}$/,  // Изменено с /^\d{20}$/ чтобы разрешить ввод
                  message: 'Корреспондентский счет должен содержать 20 цифр'
                }
              }}
              render={({ field: { ref, name, value, onChange, onBlur }, fieldState: { invalid, error } }) => (
                <Input
                  ref={ref}
                  name={name}
                  value={value}
                  onChange={(e) => {
                    const newValue = e.target.value.replace(/\D/g, '').slice(0, 20) // Ограничиваем ввод только цифрами
                    onChange(newValue)
                    handleFieldChange(name, newValue)
                  }}
                  onBlur={(e) => {
                    onBlur()
                    if (e.target.value.length > 0 && e.target.value.length !== 20) {
                      trigger(name) // Вызываем валидацию при потере фокуса
                    }
                  }}
                  label='Корреспондентский счет'
                  isRequired={isLegalEntity}
                  // isReadOnly={!!selectedBank}
                  validationBehavior='aria'
                  isInvalid={invalid}
                  errorMessage={error?.message}
                />
              )}
            />
          </div>
        )}
      </div>

      <div className='mt-4 flex w-full justify-end'>
        {showNextBtn && (
          <Button color='warning' variant='flat' type='submit'>
            {nextBtnTitle || 'Продолжить'}
          </Button>
        )}
      </div>
    </Form>
  )
}
