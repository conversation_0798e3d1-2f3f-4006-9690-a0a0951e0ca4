import React, { useMemo, useEffect, useState } from 'react'
import { RadioGroup, Radio, cn, Form, Button } from '@heroui/react'
import { useForm, Controller } from 'react-hook-form'
import { Building2Icon, CreditCardIcon } from 'lucide-react'

interface PaymentMethodType {
  name: string
  title: string
  note?: string
  icon: string
  active: boolean
  payLater?: boolean
}

interface PaymentMethodsProps {
  locale?: string
  apiSettings: Record<string, any>
  whosalePrices: boolean
  countryId?: number
  onPayment: (method: PaymentMethodType) => void
  handleNext: () => void
  onValidityChange?: (isValid: boolean) => void
  selectedPayment?: string // добавляем проп для выбранного метода оплаты
}


const CustomRadio = ({ children, ...props }: any) => (
  <Radio
    {...props}
    classNames={{
      base: cn(
        'm-0 bg-white dark:bg-default-50 hover:bg-default-100',
        'cursor-pointer rounded-lg gap-2 p-2 lg:p-4 border-2 dark:border-default-200',
        'data-[selected=true]:border-warning-500 appearance-none'
      )
    }}
  >
    {children}
  </Radio>
)

export const PaymentMethods = ({ onValidityChange, selectedPayment, ...props }: PaymentMethodsProps) => {
  const [methods, setMethods] = useState<PaymentMethodType[]>([])
  const [showNextBtn, setShowNextBtn] = useState(true)

  const { control, handleSubmit, watch, setValue, formState } = useForm({
    defaultValues: {
      paymentMethod: selectedPayment || ''
    },
    mode: 'onChange'
  })

  // Синхронизация выбранного метода оплаты с пропом selectedPayment
  useEffect(() => {
    if (selectedPayment) {
      const method = methods.find((m) => m.name === selectedPayment)
      if (method) {
        setValue('paymentMethod', selectedPayment)
        props.onPayment(method) // Гарантируем вызов onPayment при синхронизации
      }
    }
  }, [selectedPayment, methods])

  useEffect(() => {
    const baseMethods = [
      {
        title: 'Банковской картой',
        name: 'card',
        note: '',
        icon: CreditCardIcon,
        payLater: false,
        active: true
      },
      {
        title: 'По безналичному счету',
        name: 'bank',
        note: 'Этот способ оплаты только для юридических лиц: ИП, ООО и другие.',
        icon: Building2Icon,
        active: true
      }
    ]

    const euMethods = [
      {
        title: 'Bank transfer',
        name: 'bank',
        note: '',
        icon: '',
        active: props.whosalePrices
      },
      {
        title: 'Pay later',
        name: 'paylater',
        note: '',
        icon: '',
        active: props.whosalePrices
      },
      {
        title: 'Zaplata kurierowi za pobraniem',
        name: 'courierpayment',
        note: '',
        icon: '',
        active: props.whosalePrices && Number(props.countryId) === 616
      }
    ]

    const filtered = props.locale !== 'ru' ? euMethods.filter((m) => m.active) : baseMethods.filter((m) => m.active)

    setMethods(filtered)
  }, [props.locale, props.whosalePrices, props.countryId])

  useEffect(() => {    
    onValidityChange?.(formState.isValid)
  }, [formState.isValid])

  const onSubmit = (data) => {
    // console.log('Payment form data:', data)
    props.handleNext()
    setShowNextBtn(false)
  }

  return (
    <Form onSubmit={handleSubmit(onSubmit)}>
      <Controller
        control={control}
        name="paymentMethod"
        rules={{ required: 'Выберите способ оплаты' }}
        render={({ field: { ref, value, onChange }, fieldState: { invalid, error } }) => (
          <RadioGroup
            ref={ref}
            isRequired
            label='Способ оплаты'
            value={value}
            color='warning'
            onValueChange={(value) => {
              onChange(value)
              const method = methods.find((m) => m.name === value)
              if (method) {
                props.onPayment(method)
              }
            }}
            classNames={{
              base: 'gap-3',
              wrapper: 'flex flex flex-row flex-wrap lg:flex-nowrap gap-3',
              description: 'text-red-800 text-lg'
            }}
            errorMessage={error?.message}
          >
            {methods.map((method) => (
              <CustomRadio key={method.name} value={method.name}>
                <div className='flex items-center gap-3'>
                  <div className='h-8 w-8 text-warning-600 [&>svg]:h-full [&>svg]:w-full' >
                    <method.icon/>
                  </div>
                  <div>
                    <div className='font-semibold'>{method.title}</div>
                    <div className='text-sm text-default-600'>{method.note}</div>
                  </div>
                </div>
              </CustomRadio>
            ))}
          </RadioGroup>
        )}
      />
      <div className='mt-4 flex w-full justify-end'>
        {showNextBtn && (
          <Button color='warning' variant='flat' type='submit'>
            Продолжить
          </Button>
        )}
      </div>
    </Form>
  )
}
