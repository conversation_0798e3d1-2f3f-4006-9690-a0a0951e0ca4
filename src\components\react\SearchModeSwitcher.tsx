import { Tab, Tabs } from '@heroui/react'
import { FolderOpenIcon, LayoutGridIcon, Rows3Icon, TableIcon } from 'lucide-react'
import { useStore } from '@nanostores/react'
import { searchModeStore, type SearchMode } from '@/stores/searchMode'

interface Props {
  onSelect?: (key: SearchMode) => void
}

export const SearchModeSwitch = ({ onSelect }: Props) => {
  const currentMode = useStore(searchModeStore)

  const handleSelect = (key: SearchMode) => {
    //console.log('@ search mode handleSelect:')

    document.cookie = `search-mode=${key};path=/;max-age=31536000;SameSite=Strict`
    searchModeStore.set(key)
    onSelect?.(key)
    window.location.reload()
  }

  return (
    <Tabs
      selectedKey={currentMode}
      // onChange={() => //console.log('onSelectSearchMode')}
      onSelectionChange={(k) => handleSelect(k as SearchMode)}
      variant='underlined'
    >
      <Tab
        title={
          <div className='flex items-center gap-2'>
            <span>Списком</span> <Rows3Icon className='w-4' />
          </div>
        }
        key='flat'
      />
      <Tab
        title={
          <div className='flex items-center gap-2'>
            <span>По категориям</span> <FolderOpenIcon className='w-4' />
          </div>
        }
        key='category'
      />
    </Tabs>
  )
}
