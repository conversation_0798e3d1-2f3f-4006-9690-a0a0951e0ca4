import { priceFormat } from '@/lib/priceFormat'
import { useEffect, useState } from 'react'
import { TrpcReactWrapper } from '../TrcpReactWrapper'
import { trpcReact } from '@/trpc'
import { Alert } from '@heroui/react'
import { cartIsLoading, cartItems, cartTotal, setCartIsLoading, setCartSums, setWhosalePrices } from '@/stores/cart'
import { useStore } from '@tanstack/react-store'
import { Skeleton } from '@heroui/react'
import { FreeShippingCartMessage } from '../FreeShippingCartMessage'

interface Props {
  destinationIndex?: string
  destinationAddress?: string
  destinationCity?: string
  countryId?: number
  shippingType?: 'express' | 'standard' | string
  shippingPrice?: number
  isShippingLoading?: boolean
  selectedShippingMethod?: string
}

export const CartSum = (props: Props) => {
  return (
    <>
      <TrpcReactWrapper>
        <Calculator {...props} />
      </TrpcReactWrapper>
    </>
  )
}

export const Calculator = ({
  shippingType = 'standard',
  destinationIndex,
  destinationAddress = '',
  destinationCity = '',
  countryId = 643,
  selectedShippingMethod,
  shippingPrice,
  isShippingLoading
}: Props & { selectedShippingMethod?: string }) => {
  // Используем для получения данных из хранилища
  useStore(cartItems)
  useStore(cartTotal)
  const $cartTotal = useStore(cartTotal)
  const $cartIsLoading = useStore(cartIsLoading)

  // Используем для форсирования обновления при изменении shippingPrice
  const [, setForceUpdate] = useState(0)

  useEffect(() => {
    setForceUpdate((prev) => prev + 1)
    //console.log('CartSum FORCE UPDATE triggered by shippingPrice:', shippingPrice, forceUpdate);
  }, [shippingPrice])

  const { data, isLoading, isFetching, isSuccess, refetch } = trpcReact.products.getCartSum.useQuery(undefined, {
    // refetchOnMount: true,
    refetchOnReconnect: true,
    refetchOnWindowFocus: true
    // retryOnMount: true,
  })

  // Синхронизируем состояние загрузки корзины с состоянием загрузки tRPC запроса
  // useEffect(() => {
  //   // Обновляем глобальный стор cartIsLoading в зависимости от состояния загрузки запроса
  //   setCartIsLoading(isLoading || isFetching)
  // }, [isLoading, isFetching])

  useEffect(() => {
    //console.log('useEffect [data] - CartSum Query Data updated:', data, 'isSuccess:', isSuccess, 'shippingPrice PROP in useEffect[data]:', shippingPrice);
    if (isSuccess) {
      setCartSums(data)
    }
  }, [data, isSuccess, shippingPrice])

  useEffect(() => {
    //console.log('💰 Whosale Prices updated:', data?.whosalePrices);
    if (typeof data?.whosalePrices !== 'undefined') {
      setWhosalePrices(data.whosalePrices)
    }
  }, [data?.whosalePrices])

  useEffect(() => {
    const unSubscribe = cartItems.subscribe(() => {
      //console.log('🛒 CartSum cartItems.subscribe triggered, refetching cart sum');
      refetch()
    })

    return () => {
      unSubscribe()
    }
  }, [])

  return (
    <>
      {isSuccess && (
        <div>
          <div className='mb-2 translate-x-0 transform rounded-md border bg-default-50 py-2 font-bold opacity-100 transition-all duration-400 ease-out animate-in dark:border-default-200 dark:bg-default-100'>
            <div className='mx-2 flex flex-wrap items-center justify-between text-sm text-default-700 lg:text-base'>
              <div className='flex items-center justify-between space-x-5'>
                <div className='hidden items-center font-normal text-default-700 sm:flex'>
                  <span>{'Цены'}: </span>
                  <div className={`${data.whosalePrices ? 'bg-warning-100' : 'bg-default-100'} ml-2 rounded-lg px-2 py-1 font-bold`}>
                    {data.whosalePrices ? 'Опт' : 'Розница'}
                  </div>
                </div>

                {data.personalDiscountValue && Number(data.personalDiscountValue) > 0 ? (
                  <div className='flex items-center font-normal text-default-700'>
                    <span>{'Скидка'}: </span>
                    <div className='ml-2 rounded-md bg-warning-100 px-2 py-1 font-bold'>{data.personalDiscountValue}%</div>
                  </div>
                ) : (
                  ''
                )}

                {!data.personalDiscountValue && data.bigDiscount ? (
                  <div className='flex items-center font-normal text-default-700'>
                    <span>{'Скидка'}: </span>
                    <div className='ml-2 rounded-md bg-warning-100 px-2 py-1 font-bold'>{data.bigDiscountValue}%</div>
                  </div>
                ) : (
                  ''
                )}
              </div>

              {!data.whosalePrices && (
                <div className='hidden text-default-700 md:block'>
                  <Alert className='text-sm' variant='bordered'>
                    <div className='font-medium'>
                      {'Цены изменятся на оптовые от суммы'}:<span className='font-semibold'> {priceFormat(10000)}</span>
                    </div>
                  </Alert>
                </div>
              )}
              <div className='flex flex-col gap-3'>
                {!!data.sum && (
                  <div className='flex flex-col gap-3'>
                    <div className='flex items-center gap-2'>
                      <span className='text-sm text-default-800'>Сумма:</span>
                      <div className='flex items-center'>
                        {!!data.discountValue && (
                          <span className='mr-1 text-xs text-default-700 line-through'>{priceFormat(data.discountValue + data.sum)}</span>
                        )}
                        <span className='text-sm font-medium text-default-900'>{priceFormat(data.sum)}</span>
                      </div>
                    </div>

                    {isShippingLoading ? (
                      <Skeleton className='h-4 w-20' />
                    ) : (
                      <div>
                        {shippingPrice && shippingPrice > 0 ? (
                          <div>
                            <div className='flex items-center gap-2'>
                              <span className='text-sm text-default-800'>Доставка:</span>
                              <span className='text-sm text-default-900'>{priceFormat(shippingPrice)}</span>
                            </div>

                            <div className='mt-2 flex items-center gap-2'>
                              <span className='text-base text-default-800'>Итого: </span>
                              <span className='font-semibold text-default-900'>{priceFormat(shippingPrice + data.sum)}</span>
                            </div>
                          </div>
                        ) : (
                          ''
                        )}
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
          {!$cartIsLoading && $cartTotal > 0 && data.whosalePrices && (
            <div className='flex justify-center'>
              <div>
                <FreeShippingCartMessage />
              </div>
            </div>
          )}
        </div>
      )}
    </>
  )
}
