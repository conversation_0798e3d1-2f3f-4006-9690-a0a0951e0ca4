---
import { ProfileSettings } from '@components/react/users/ProfileSettings'
import Layout from '@layouts/Layout.astro'
import { Skeleton } from '@heroui/react'
import { BackHistoryButton } from '@components/react/BackHistoryButton'

// const cookieHeader = Astro.request.headers.get('cookie') || ''

// const res = await fetch('/api/auth/login/check', {
//   credentials: 'include',
//   headers: {
//     'Content-Type': 'application/json',
//     cookie: cookieHeader
//   }
// })

// const clientData = await res.json()
---

<Layout title='Настройки'>
  <div>
    <BackHistoryButton goto="/" variant='light' size='sm' label='Назад' client:idle />
  </div>
  <ProfileSettings client:only='react'>
    <div slot='fallback'>
      <div class='mx-auto max-w-3xl p-4'>
        <div class='mb-6'>
          <Skeleton className='h-8 w-40 rounded-lg' />
        </div>

        <div class='space-y-8'>
          {/* Скелетон для PersonalForm */}
          <div class='rounded border p-2 dark:border-default-200 md:p-3 xl:p-5'>
            <Skeleton className='mb-4 h-6 w-32 rounded-lg' />
            <div class='space-y-4'>
              <Skeleton className='h-12 w-full rounded-lg' />
              <Skeleton className='h-12 w-full rounded-lg' />
              <Skeleton className='h-12 w-full rounded-lg' />
            </div>
            <div class='mt-4 flex w-full justify-end'>
              <Skeleton disableAnimation className='h-10 w-28 rounded-lg bg-warning-100' />
            </div>
          </div>

          {/* Скелетон для AddressForm */}
          <div class='rounded border p-2 dark:border-default-200 md:p-3 xl:p-5'>
            <Skeleton className='mb-4 h-6 w-24 rounded-lg' />
            <div class='space-y-4'>
              <div class='flex flex-wrap gap-4'>
                <Skeleton className='h-12 w-52 rounded-lg' />
                {/* Страна */}
                <Skeleton className='h-12 flex-1' />
                {/* Город */}
              </div>
              <Skeleton className='h-12 w-80' />
              {/* Улица */}
              <div class='grid grid-cols-2 gap-4'>
                <Skeleton className='h-12 rounded-lg' />
                {/* Дом */}
                <Skeleton className='h-12 rounded-lg' />
                {/* Квартира */}
              </div>
              <Skeleton className='h-12 w-40 rounded-lg' />
              {/* Индекс */}
              <div class='mt-4 flex w-full justify-end'>
                <Skeleton disableAnimation className='h-10 w-28 rounded-lg bg-warning-100' />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ProfileSettings>
</Layout>
