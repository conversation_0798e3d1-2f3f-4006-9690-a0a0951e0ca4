import { useState, useEffect, useRef } from 'react'

interface TypewriterPlaceholderProps {
  texts: string[]
  typingSpeed?: number
  deletingSpeed?: number
  delayAfterTyping?: number
}

export const TypewriterPlaceholder = ({ 
  texts, 
  typingSpeed = 100, 
  deletingSpeed = 80, 
  delayAfterTyping = 2500 
}: TypewriterPlaceholderProps) => {
  const [currentText, setCurrentText] = useState('')
  const [isDeleting, setIsDeleting] = useState(false)
  const [textIndex, setTextIndex] = useState(0)
  const [delta, setDelta] = useState(typingSpeed)
  
  // Используем useRef для сохранения значения между рендерами
  const timerRef = useRef<NodeJS.Timeout | null>(null)
  
  useEffect(() => {
    // Очищаем предыдущий таймер
    if (timerRef.current) {
      clearTimeout(timerRef.current)
    }
    
    // Функция для анимации текста
    const tick = () => {
      const fullText = texts[textIndex]
      
      if (isDeleting) {
        // Удаляем по одному символу
        setCurrentText(prev => prev.substring(0, prev.length - 1))
        setDelta(deletingSpeed)
      } else {
        // Добавляем по одному символу
        setCurrentText(prev => fullText.substring(0, prev.length + 1))
        setDelta(typingSpeed)
      }
      
      // Логика переключения между печатью и удалением
      if (!isDeleting && currentText === fullText) {
        // Текст полностью напечатан, ждем и начинаем удаление
        setDelta(delayAfterTyping)
        setIsDeleting(true)
      } else if (isDeleting && currentText === '') {
        // Текст полностью удален, переходим к следующему
        setIsDeleting(false)
        setTextIndex((prev) => (prev + 1) % texts.length)
        setDelta(typingSpeed)
      }
      
      // Устанавливаем следующий таймер
      timerRef.current = setTimeout(tick, delta)
    }
    
    // Запускаем анимацию
    timerRef.current = setTimeout(tick, delta)
    
    // Очищаем таймер при размонтировании компонента
    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current)
      }
    }
  }, [currentText, delta, isDeleting, textIndex, texts, typingSpeed, deletingSpeed, delayAfterTyping])
  
  return currentText
}
