---
import { Pagin<PERSON> } from './react/Paginator'

interface Props {
  limit?: number
  total?: number
  initPage?: number
  isSticky?: boolean
  isMobile?: boolean
  showSelectLimit?: boolean
  showPageInput?: boolean
}

const { initPage = 1, limit = 200, total = 0, isSticky = true, isMobile = false, showSelectLimit, showPageInput = false } = Astro.props

// SSR-якоря для SEO (prev/next)
const pageCount = Number(total) || 1
const currentPage = Math.max(1, Math.min(Number(initPage), pageCount))
const getPageHref = (pageNum: number) => {
  const url = new URL(Astro.url)
  url.searchParams.set('page', String(pageNum))
  return url.pathname + url.search
}
---

<div class={(isSticky ? 'sticky bottom-14' : '') + ' md:mr-5 flex flex-col items-center gap-2 pt-3 md:items-end lg:bottom-3 lg:mr-10'}>
  <!-- SSR-навигация для поисковиков и пользователей без JS -->
  <nav aria-label='Пагинация' class='rounded-lg bg-default-100 p-1 px-2 text-sm text-default-600'>
    {
      currentPage > 1 && (
        <a rel='prev' href={getPageHref(currentPage - 1)} class='underline hover:no-underline'>
          Предыдущая
        </a>
      )
    }
    <!-- <span class='mx-2'>Стр. {currentPage} из {pageCount}</span> -->
    <span class='mx-2'>Стр. <input input-page-n type='number' value={currentPage} class='w-10 rounded text-center' /> из {pageCount}</span>
    {
      currentPage < pageCount && (
        <a rel='next' href={getPageHref(currentPage + 1)} class='underline hover:no-underline'>
          Следующая
        </a>
      )
    }
  </nav>

  {
    !isMobile && (
      <Paginator
        showPageInput={false}
        showSelectLimit={showSelectLimit}
        isMobile={isMobile}
        limit={limit}
        total={total}
        initPage={initPage}
        client:only='react'
      />
    )
  }
</div>
 
