---
import { priceFormat } from '@/lib/priceFormat'
import { QtyInput } from './react/QtyInput'
import { Tooltip } from '@heroui/react'
import { CameraIcon } from 'lucide-react'
import { PhotoTooltip } from './react/PhotoTooltip'

interface Props {
  tableData: any
  activeSorts?: { column: string; direction: 'asc' | 'desc' }[]
}

const { tableData, activeSorts = [] } = Astro.props
const { products, category } = tableData
//console.log('🚀 ~ category:', category)

function getSortIcon(columnKey: string) {
  const sort = activeSorts.find((s) => s.column === columnKey)
  return sort?.direction === 'asc' ? '↑' : sort?.direction === 'desc' ? '↓' : ''
}

function getSortOrder(columnKey: string) {
  return activeSorts.findIndex((s) => s.column === columnKey) + 1
}
---

<div class='overflow-x-auto'>
  <table class='ttable-auto min-w-full rounded-lg bg-white dark:bg-default-50'>
    <thead>
      <tr class='rounded-lg bg-default-100'>
        {
          [...(tableData.category?.columns || tableData.columns)].map((column) => (
            <th class='px-6 py-3 text-left font-medium uppercase tracking-wider text-default-700'>
              <div class='flex items-center gap-2 text-sm'>
                <span>{column.title}</span>
                {column.sorted && (
                  <div class='flex items-center'>
                    <span class='text-primary-600'>{getSortIcon(column.keyname)}</span>
                    {activeSorts.some((s) => s.column === column.keyname) && <span class='ml-1 text-xs text-default-500'>{getSortOrder(column.keyname)}</span>}
                  </div>
                )}
              </div>
            </th>
          ))
        }

        <th class='px-6 py-3 text-left font-medium uppercase tracking-wider text-default-700'>
          <span>Цена</span>
        </th>
        <th class='px-6 py-3 text-left font-medium uppercase tracking-wider text-default-700'>
          <span>Опт</span>
        </th>
        <th class='px-6 py-3 text-left font-medium uppercase tracking-wider text-default-700'>
          <span>Фото</span>
        </th>
        <th class='px-6 py-3 text-left font-medium uppercase tracking-wider text-default-700'>
          <span>В корзину</span>
        </th>
      </tr>
    </thead>
    <tbody class='divide-y divide-default-200'>
      {
        tableData.products?.data ||
          tableData.data.map((product) => (
            <tr class='even:bg-default-50 hover:bg-default-50'>
              {[...(tableData.category?.columns || tableData.columns)].map((column) => (
                <td class='whitespace-nowrap px-6 py-4 text-sm text-default-700'>{product[column.keyname]}</td>
              ))}
              <td class='whitespace-nowrap px-6 py-4 text-sm text-default-700'>{priceFormat(product.prod_price)}</td>
              <td class='whitespace-nowrap px-6 py-4 text-sm text-default-700'>{priceFormat(product.whosaleprice)}</td>
              <td class='whitespace-nowrap px-6 py-4 text-sm text-default-700'>{<PhotoTooltip client:visible product={product} />}</td>

              <td class='whitespace-nowrap px-6 py-4 text-sm text-default-700'>{<QtyInput mini client:visible product={product} />}</td>
            </tr>
          ))
      }
    </tbody>
  </table>
</div>
