import { useEffect, useRef, useState, type ReactNode } from 'react'
import { QtyInput } from './QtyInput'
import type { CategoryProduct } from '@/types/GetCategoryProduct'

interface LazyQtyInputProps {
  initialValue?: number
  product: Partial<CategoryProduct> & { prod_id: number; inStock?: boolean; prod_count?: number }
  isLoadingState?: boolean
  label?: string | ReactNode
  mini?: boolean
  showRemoveBtn?: boolean
  size?: 'sm' | 'md' | 'lg'
  cartMode?: boolean
  onAdded?: (product: Partial<CategoryProduct>) => void
  debounceDelay?: number
  preorderMode?: 'modal' | 'button'
}

export const LazyQtyInput = ({ product, initialValue, ...restProps }: LazyQtyInputProps) => {
  const [isVisible, setIsVisible] = useState(false)
  const containerRef = useRef(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
          observer.disconnect()
        }
      },
      { threshold: 0.1 }
    )

    if (containerRef.current) {
      observer.observe(containerRef.current)
    }

    return () => observer.disconnect()
  }, [])

  return (
    <div ref={containerRef}>
      {isVisible ? (
        <QtyInput product={product} initialValue={initialValue} {...restProps} />
      ) : (
        <div className='w-[120px] h-[32px]' /> // placeholder
      )}
    </div>
  )
}
