import type { CategoryProduct } from '@/types/GetCategoryProduct'
import { Button, Form, Input, Card, CardBody, CardHeader, Divider, NumberInput, Alert } from "@heroui/react"
import { useState, useEffect } from 'react'
import { addToast } from '@heroui/toast'


interface Props {
  product: CategoryProduct
  onClose?: () => void
}

type FormValue = {
  qty: number
  email: string
  phone?: string
  product: CategoryProduct
}

export const ProductPreorder = ({ product, onClose }: Props) => {
  const [step, setStep] = useState(0)
  const [isLoading, setIsLoading] = useState(false)
  const [formData, setFormData] = useState<FormValue>({
    qty: 1,
    email: '',
    phone: '',
    product: product
  })

  useEffect(() => {
    try {
      fetch(`/api/statistics/w/${product.prod_analogsku}`)
    } catch (error) {
      console.error(error)
    }
  }, [product])

  const clear = () => {
    setStep(0)
    setFormData({
      qty: 1,
      email: '',
      phone: '',
      product: product
    })
    onClose?.()
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (step === 0) {
      if (formData.qty < 1) return
      setStep(1)
      return
    }

    if (step === 1) {
      if (!formData.email || (formData.phone && formData.phone.length < 3)) {
        return
      }

      try {
        setIsLoading(true)
        const response = await fetch('/api/preorder', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(formData)
        })

        const data = await response.json()
        
        addToast({
          title: data.msg || 'Предзаказ оформлен',
          variant: 'bordered',
          color: 'success',
          timeout: 4000
        })
        
        setTimeout(() => {
          clear()
        }, 500)
      } catch (error) {
        console.error('Error submitting preorder:', error)
        addToast({
          title: 'Ошибка',
          description: 'Произошла ошибка при оформлении предзаказа',
          variant: 'bordered',
          color: 'danger'
        })
        setIsLoading(false)
      }
    }
  }

  return (
    <Card className="w-full max-w-xl mx-auto shadow-none">
      <CardHeader className="flex gap-3 justify-center items-center pb-2">
        {/* <ShoppingBagIcon className="w-8 h-8 text-warning" /> */}
        <div className="flex flex-col gap-1 items-center justify-center">
          <h3 className="text-xl font-medium">Оформить предзаказ</h3>
          <Alert color='danger'>Товар отсутствует на складе</Alert>
        </div>
      </CardHeader>
      <Divider/>
      <CardBody className="px-8 py-6">
        <div className="mb-6">
          <h4 className="text-default-600 text-small font-medium mb-1">Товар</h4>
          <p className="text-large font-medium">{product.prod_purpose} {product.prod_sku || ''}</p>
        </div>

        <Form className="space-y-6" onSubmit={handleSubmit}>
          {step === 0 ? (
            <div className="space-y-4">
              <h4 className="text-medium font-medium">Укажите необходимое количество</h4>
              <NumberInput
                isRequired
                label="Количество"
                min={1}
                max={3000}
                value={formData.qty}
                size="lg"
                className="max-w-xs"
                onChange={(e) => setFormData({...formData, qty: parseInt(e.target.value)})}
              />
            </div>
          ) : (
            <div className="space-y-4">
              <h4 className="text-medium font-medium">Контактные данные</h4>
              <div className="space-y-4">
                <Input
                  isRequired
                  type="email"
                  label="Email"
                  placeholder="<EMAIL>"
                  size="lg"
                  value={formData.email}
                  onChange={(e) => setFormData({...formData, email: e.target.value})}
                />
                <Input
                  type="tel"
                  label="Телефон"
                  placeholder="+7 (___) ___-__-__"
                  size="lg"
                  value={formData.phone}
                  onChange={(e) => setFormData({...formData, phone: e.target.value})}
                />
              </div>
            </div>
          )}
          
          <div className="flex justify-center pt-4">
            <Button 
              isLoading={isLoading}
              type="submit"
              className="px-8 min-w-[200px]"
            >
              {step === 0 ? "Продолжить" : "Оформить предзаказ"}
            </Button>
          </div>
        </Form>
      </CardBody>
    </Card>
  )
}
