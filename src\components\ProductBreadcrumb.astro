---
import { trpc } from '@/trpc'
import BreadcrumbsNav from '@/components/react/Breadcrumbs'
import type { CategoryProduct } from '@/types/GetCategoryProduct'

interface Props {
  product: CategoryProduct
}

const { product } = Astro.props
const breadcrumbsItems = await trpc.products.getProductBreadcrumbs.query(product?.prod_id)

let bcItems = []
if (breadcrumbsItems?.length > 0) {
  bcItems = [
    ...breadcrumbsItems?.filter((i) => i).map((i) => ({ key: i.fullPath, label: i.cat_title })),
    { key: product.prod_id, label: `${product.prod_sku}` }
  ]
}
---

<div>
  <BreadcrumbsNav client:visible prefix='catalog/' items={bcItems} />
</div>
