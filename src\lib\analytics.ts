/**
 * Утилиты для работы с аналитикой (Google Analytics и Яндекс Метрика)
 */

// Типы для Google Analytics
interface GTagEvent {
  action: string
  category: string
  label: string
  value?: number
}

/**
 * Отправка события в Google Analytics
 * @param event Объект события
 */
export const sendGAEvent = (event: GTagEvent): void => {
  if (typeof window !== 'undefined' && typeof window.gtag === 'function') {
    window.gtag('event', event.action, {
      event_category: event.category,
      event_label: event.label,
      value: event.value
    })
  }
}

/**
 * Отправка события просмотра страницы в Google Analytics
 * @param path Путь страницы
 */
export const sendGAPageView = (path: string): void => {
  if (typeof window !== 'undefined' && typeof window.gtag === 'function') {
    window.gtag('config', import.meta.env.PUBLIC_GA_MEASUREMENT_ID || '265614037', {
      page_path: path
    })
  }
}

/**
 * Отправка события клика в Google Analytics
 * @param category Категория события
 * @param label Метка события
 * @param value Значение события (опционально)
 */
export const sendGAClickEvent = (category: string, label: string, value?: number): void => {
  sendGAEvent({
    action: 'click',
    category,
    label,
    value
  })
}

/**
 * Отправка события поиска в Google Analytics
 * @param searchTerm Поисковый запрос
 */
export const sendGASearchEvent = (searchTerm: string): void => {
  if (typeof window !== 'undefined' && typeof window.gtag === 'function') {
    window.gtag('event', 'search', {
      search_term: searchTerm
    })
  }
}

/**
 * Отправка события добавления в корзину в Google Analytics
 * @param productId ID товара
 * @param productName Название товара
 * @param price Цена товара
 * @param quantity Количество
 */
export const sendGAAddToCartEvent = (
  productId: string | number,
  productName: string,
  price: number,
  quantity: number
): void => {
  if (typeof window !== 'undefined' && typeof window.gtag === 'function') {
    window.gtag('event', 'add_to_cart', {
      currency: 'RUB',
      value: price * quantity,
      items: [
        {
          item_id: productId,
          item_name: productName,
          price,
          quantity
        }
      ]
    })
  }
}

/**
 * Отправка события начала оформления заказа в Google Analytics
 * @param products Массив товаров
 */
export const sendGABeginCheckoutEvent = (
  products: Array<{
    productId: string | number
    productName: string
    price: number
    quantity: number
  }>
): void => {
  if (typeof window !== 'undefined' && typeof window.gtag === 'function') {
    const totalValue = products.reduce((sum, product) => sum + product.price * product.quantity, 0)

    window.gtag('event', 'begin_checkout', {
      currency: 'RUB',
      value: totalValue,
      items: products.map(product => ({
        item_id: product.productId,
        item_name: product.productName,
        price: product.price,
        quantity: product.quantity
      }))
    })
  }
}

/**
 * Отправка события завершения покупки в Google Analytics
 * @param transactionId ID транзакции
 * @param totalValue Общая стоимость
 * @param products Массив товаров
 */
export const sendGAPurchaseEvent = (
  transactionId: string,
  totalValue: number,
  products: Array<{
    productId: string | number
    productName: string
    price: number
    quantity: number
  }>
): void => {
  if (typeof window !== 'undefined' && typeof window.gtag === 'function') {
    window.gtag('event', 'purchase', {
      transaction_id: transactionId,
      value: totalValue,
      currency: 'RUB',
      tax: 0,
      shipping: 0,
      items: products.map(product => ({
        item_id: product.productId,
        item_name: product.productName,
        price: product.price,
        quantity: product.quantity
      }))
    })
  }
}

/**
 * Яндекс Метрика
 */

/**
 * Отправка события в Яндекс Метрику
 * @param eventName Название события
 * @param params Параметры события
 */
export const sendYMEvent = (eventName: string, params?: Record<string, any>): void => {
  if (typeof window !== 'undefined' && typeof window.ym === 'function') {
    const counterId = import.meta.env.PUBLIC_YM_COUNTER_ID || '34401280'
    window.ym(counterId, 'reachGoal', eventName, params)
  }
}

/**
 * Отправка события просмотра страницы в Яндекс Метрику
 * @param url URL страницы
 */
export const sendYMPageView = (url: string): void => {
  if (typeof window !== 'undefined' && typeof window.ym === 'function') {
    const counterId = import.meta.env.PUBLIC_YM_COUNTER_ID || '34401280'
    window.ym(counterId, 'hit', url)
  }
}

/**
 * Отправка события клика в Яндекс Метрику
 * @param category Категория события
 * @param label Метка события
 */
export const sendYMClickEvent = (category: string, label: string): void => {
  sendYMEvent('click', { category, label })
}

/**
 * Отправка события поиска в Яндекс Метрику
 * @param searchTerm Поисковый запрос
 */
export const sendYMSearchEvent = (searchTerm: string): void => {
  sendYMEvent('search', { search_term: searchTerm })
}

/**
 * Отправка события добавления в корзину в Яндекс Метрику
 * @param productId ID товара
 * @param productName Название товара
 * @param price Цена товара
 * @param quantity Количество
 */
export const sendYMAddToCartEvent = (
  productId: string | number,
  productName: string,
  price: number,
  quantity: number
): void => {
  sendYMEvent('add_to_cart', {
    product_id: productId,
    product_name: productName,
    price,
    quantity
  })
}

/**
 * Отправка события начала оформления заказа в Яндекс Метрику
 */
export const sendYMBeginCheckoutEvent = (): void => {
  sendYMEvent('begin_checkout')
}

/**
 * Отправка события завершения покупки в Яндекс Метрику
 * @param orderId ID заказа
 * @param totalValue Общая стоимость
 */
export const sendYMPurchaseEvent = (orderId: string, totalValue: number): void => {
  sendYMEvent('purchase', {
    order_id: orderId,
    value: totalValue
  })
}

// Расширение типов для Window
declare global {
  interface Window {
    dataLayer: any[]
    gtag: (...args: any[]) => void
    ym: (counterId: string | number, action: string, ...args: any[]) => void
  }
}
