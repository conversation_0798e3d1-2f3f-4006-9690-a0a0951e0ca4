import type { ChangeEvent } from 'react'

export interface FormData {
  email: string
  password: string
  firstName: string
  lastName: string
  phone: string
  country: string
  countryData: Country | null
  location: string
  locationData: Location | null
  street: string
  house: string
  apartment: string
  zipCode: string
}

export interface FormComponentProps {
  data: FormData
  onChange: (e: ChangeEvent<HTMLInputElement>) => void
  handleNext: () => void
  onValidityChange?: (isValid: boolean) => void
}

export interface ShippingLocation {
  location: string
  index: string | null
  city_fias_id: string | null
  settlement_fias_id: string | null
  street_with_type: string | null
  // Добавляем поля, которые используются в коде
  uniqueId?: string
  // Дополнительные поля для совместимости
  [key: string]: any
}

export interface Country {
  country_id: number
  iso: string
  title: string
}


