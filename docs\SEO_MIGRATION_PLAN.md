# План миграции SEO для страниц товаров

## Текущая ситуация

У нас есть 3 типа страниц товаров:

1. **`/catalog/product/[id]`** - Оригинальная страница с полной коммерческой информацией
   - Содержит все кросс-номера и аналоги
   - Полная SEO оптимизация
   - Индексируется поисковиками (`index, follow`)

2. **`/catalog/products/[id]`** - Упрощенная клиентская версия
   - Без коммерческой информации о кросс-номерах
   - Урезанный контент для клиентов
   - **НЕ индексируется** (`noindex, follow`)
   - Canonical указывает на `/catalog/product/[id]`

3. **`/catalog/item/[id]`** - Виртуальные товары (кросс-номера)
   - Отдельные страницы для каждого кросс-номера
   - **НЕ индексируется** (`noindex, follow`)
   - Canonical указывает на `/catalog/product/[id]`

## Этап 1: Переходный период (текущий) - 2-3 месяца

### ✅ Уже настроено:

1. **Canonical URLs:**
   - `/catalog/product/[id]` → canonical на себя
   - `/catalog/products/[id]` → canonical на `/catalog/product/[id]`
   - `/catalog/item/[id]` → canonical на `/catalog/product/[id]`

2. **Robots meta:**
   - `/catalog/product/[id]` → `index, follow`
   - `/catalog/products/[id]` → `noindex, follow`
   - `/catalog/item/[id]` → `noindex, follow`

3. **Alternate links:**
   - `/catalog/product/[id]` содержит ссылки на альтернативные версии

### Результат этапа 1:
- Поисковики видят только оригинальные страницы `/catalog/product/[id]`
- Новые страницы не создают дублированный контент
- Пользователи могут использовать все 3 версии
- Нет штрафов от поисковиков

## Этап 2: Подготовка к миграции - через 2-3 месяца

### Действия:

1. **Анализ трафика:**
   - Проверить, что новые страницы работают корректно
   - Убедиться в стабильности пользовательского опыта
   - Проанализировать поведение пользователей

2. **Подготовка редиректов:**
   - Настроить 301 редиректы с `/catalog/product/[id]` на `/catalog/products/[id]`
   - Обновить внутренние ссылки на сайте
   - Подготовить обновление sitemap.xml

3. **Обновление SEO настроек новых страниц:**
   ```astro
   <!-- На /catalog/products/[id] изменить: -->
   <link rel='canonical' href={currentUrl} />
   <meta name='robots' content='index, follow' />
   ```

## Этап 3: Миграция - через 3-4 месяца

### Действия:

1. **Включить редиректы:**
   - Активировать 301 редиректы
   - Обновить robots.txt если нужно
   - Обновить sitemap.xml

2. **Обновить SEO новых страниц:**
   - Изменить canonical на себя
   - Изменить robots на `index, follow`
   - Обновить structured data

3. **Мониторинг:**
   - Следить за позициями в поисковой выдаче
   - Контролировать индексацию новых страниц
   - Проверять отсутствие ошибок в Search Console

## Этап 4: Завершение миграции - через 4-6 месяцев

### Действия:

1. **Удаление старых страниц:**
   - Удалить файлы `/catalog/product/[id].astro`
   - Убрать редиректы (они больше не нужны)
   - Очистить кеш CDN

2. **Финальная проверка:**
   - Убедиться что все ссылки работают
   - Проверить отсутствие 404 ошибок
   - Подтвердить корректную индексацию

## Риски и меры предосторожности

### Риски:
- Временная потеря позиций при миграции
- Возможные технические ошибки при редиректах
- Потеря части трафика

### Меры предосторожности:
- Постепенная миграция по частям (по категориям)
- Тщательное тестирование на staging
- Резервное копирование перед каждым этапом
- Мониторинг метрик на каждом этапе

## Контрольные точки

### Через 1 месяц:
- [ ] Проверить отсутствие дублированного контента в Search Console
- [ ] Убедиться что canonical работают корректно
- [ ] Проанализировать поведение пользователей

### Через 2 месяца:
- [ ] Подготовить план редиректов
- [ ] Протестировать новые SEO настройки на staging
- [ ] Обновить внутренние ссылки

### Через 3 месяца:
- [ ] Запустить миграцию
- [ ] Активировать мониторинг позиций
- [ ] Проверить корректность индексации

### Через 6 месяцев:
- [ ] Завершить миграцию
- [ ] Удалить старые файлы
- [ ] Провести финальный аудит SEO

## Команды для выполнения

### Этап 2 - Обновление SEO настроек:
```bash
# Обновить canonical на новых страницах
# В /catalog/products/[id].astro:
<link rel='canonical' href={currentUrl} />
<meta name='robots' content='index, follow' />

# В /catalog/item/[id].astro:
<link rel='canonical' href={currentUrl} />
<meta name='robots' content='index, follow' />
```

### Этап 3 - Настройка редиректов:
```javascript
// В astro.config.mjs или middleware
export function onRequest(context, next) {
  const url = context.url.pathname;
  if (url.startsWith('/catalog/product/')) {
    const id = url.split('/').pop();
    return Response.redirect(`/catalog/products/${id}`, 301);
  }
  return next();
}
```

### Этап 4 - Удаление старых файлов:
```bash
rm src/pages/catalog/product/[id].astro
rm src/pages/catalog/product/notfound.astro
```

## Заключение

Этот план обеспечивает безопасную миграцию без потери SEO позиций и штрафов от поисковиков. Ключевые принципы:

1. **Постепенность** - каждый этап длится 1-2 месяца
2. **Безопасность** - canonical и noindex защищают от дублированного контента
3. **Мониторинг** - контроль на каждом этапе
4. **Обратимость** - возможность откатить изменения при проблемах
