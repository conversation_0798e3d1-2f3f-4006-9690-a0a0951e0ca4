# План миграции SEO для страниц товаров

## Текущая ситуация

У нас есть 3 **НЕЗАВИСИМЫХ** типа страниц товаров:

1. **`/catalog/product/[id]`** - Полная страница со ВСЕЙ информацией
   - Содержит заводской артикул + все кросс-номера и аналоги
   - Полная коммерческая информация (связи между номерами)
   - Индексируется поисковиками (`index, follow`)
   - **Будет удалена** после миграции для защиты коммерческой информации

2. **`/catalog/products/[id]`** - Страница ЗАВОДСКОГО артикула
   - Только заводской номер без кросс-номеров
   - Защищенная от воровства коммерческой информации
   - **Индексируется** (`index, follow`)
   - **Основная страница** после миграции

3. **`/catalog/item/[id]`** - Страница ОТДЕЛЬНОГО кросс-номера
   - Каждый кросс-номер как отдельный товар
   - Без связи с заводским артикулом (защита от воровства)
   - **Индексируется** (`index, follow`)
   - **Дополнительные страницы** для SEO охвата

## Этап 1: Переходный период (текущий) - 2-3 месяца

### ✅ Уже настроено:

1. **Canonical URLs (все страницы самостоятельные):**
   - `/catalog/product/[id]` → canonical на себя
   - `/catalog/products/[id]` → canonical на себя
   - `/catalog/item/[id]` → canonical на себя

2. **Robots meta (все индексируются):**
   - `/catalog/product/[id]` → `index, follow`
   - `/catalog/products/[id]` → `index, follow`
   - `/catalog/item/[id]` → `index, follow`

3. **Контентная стратегия:**
   - Каждая страница имеет уникальный контент
   - Нет дублирования между страницами
   - Разные ключевые слова и фокус

### Результат этапа 1:
- Все 3 типа страниц индексируются независимо
- Каждая страница конкурирует по своим ключевым словам
- Максимальный SEO охват по всем типам запросов
- Защита коммерческой информации от конкурентов

## Этап 2: Анализ и оптимизация - через 2-3 месяца

### Действия:

1. **Анализ трафика и позиций:**
   - Проверить индексацию всех 3 типов страниц
   - Проанализировать позиции по разным типам запросов
   - Убедиться в отсутствии каннибализации ключевых слов

2. **Оптимизация контента:**
   - Улучшить уникальность контента на каждом типе страниц
   - Добавить специфические ключевые слова для каждого типа
   - Оптимизировать внутреннюю перелинковку

3. **Мониторинг конкурентов:**
   - Убедиться что конкуренты не могут легко извлечь связи номеров
   - Проверить эффективность защиты коммерческой информации

## Этап 3: Подготовка к удалению старых страниц - через 3-4 месяца

### Действия:

1. **Анализ эффективности:**
   - Сравнить трафик между старыми и новыми страницами
   - Убедиться что новые страницы получают достаточно трафика
   - Проверить что защита коммерческой информации работает

2. **Подготовка редиректов:**
   - Настроить 301 редиректы с `/catalog/product/[id]` на `/catalog/products/[id]`
   - Обновить внутренние ссылки на сайте
   - Подготовить обновление sitemap.xml

3. **Уведомление пользователей:**
   - Добавить уведомления на старые страницы о переходе
   - Обновить документацию и инструкции

## Этап 4: Удаление старых страниц - через 4-6 месяцев

### Действия:

1. **Активация редиректов:**
   - Включить 301 редиректы с `/catalog/product/[id]` на `/catalog/products/[id]`
   - Обновить sitemap.xml (убрать старые, добавить новые)
   - Уведомить поисковики через Search Console

2. **Удаление старых страниц:**
   - Удалить файлы `/catalog/product/[id].astro`
   - Очистить кеш CDN
   - Убрать старые URL из внутренних ссылок

3. **Финальная проверка:**
   - Убедиться что все редиректы работают
   - Проверить отсутствие 404 ошибок
   - Подтвердить корректную индексацию новых страниц

## Риски и меры предосторожности

### Риски:
- Каннибализация ключевых слов между страницами
- Возможное снижение позиций из-за разделения авторитета
- Сложность в управлении 3 типами страниц
- Риск обнаружения связей конкурентами

### Меры предосторожности:
- Четкое разделение ключевых слов между типами страниц
- Уникальный контент для каждого типа страниц
- Мониторинг позиций по всем типам запросов
- Регулярная проверка защиты коммерческой информации
- A/B тестирование эффективности разделения

## Контрольные точки

### Через 1 месяц:
- [ ] Проверить отсутствие дублированного контента в Search Console
- [ ] Убедиться что canonical работают корректно
- [ ] Проанализировать поведение пользователей

### Через 2 месяца:
- [ ] Подготовить план редиректов
- [ ] Протестировать новые SEO настройки на staging
- [ ] Обновить внутренние ссылки

### Через 3 месяца:
- [ ] Запустить миграцию
- [ ] Активировать мониторинг позиций
- [ ] Проверить корректность индексации

### Через 6 месяцев:
- [ ] Завершить миграцию
- [ ] Удалить старые файлы
- [ ] Провести финальный аудит SEO

## Команды для выполнения

### Этап 2 - Обновление SEO настроек:
```bash
# Обновить canonical на новых страницах
# В /catalog/products/[id].astro:
<link rel='canonical' href={currentUrl} />
<meta name='robots' content='index, follow' />

# В /catalog/item/[id].astro:
<link rel='canonical' href={currentUrl} />
<meta name='robots' content='index, follow' />
```

### Этап 3 - Настройка редиректов:
```javascript
// В astro.config.mjs или middleware
export function onRequest(context, next) {
  const url = context.url.pathname;
  if (url.startsWith('/catalog/product/')) {
    const id = url.split('/').pop();
    return Response.redirect(`/catalog/products/${id}`, 301);
  }
  return next();
}
```

### Этап 4 - Удаление старых файлов:
```bash
rm src/pages/catalog/product/[id].astro
rm src/pages/catalog/product/notfound.astro
```

## Заключение

Этот план обеспечивает безопасную миграцию без потери SEO позиций и штрафов от поисковиков. Ключевые принципы:

1. **Постепенность** - каждый этап длится 1-2 месяца
2. **Безопасность** - canonical и noindex защищают от дублированного контента
3. **Мониторинг** - контроль на каждом этапе
4. **Обратимость** - возможность откатить изменения при проблемах
