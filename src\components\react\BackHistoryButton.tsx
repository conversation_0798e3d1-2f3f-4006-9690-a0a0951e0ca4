import { Button } from '@heroui/react'
import { navigate } from 'astro:transitions/client'
import { MoveLeftIcon } from 'lucide-react'

export const BackHistoryButton = ({ label = 'Вернуться назад', isIconOnly = false, size = 'md', variant = 'flat', goto }) => {
  function handler() {
    if (goto) {
      navigate(goto)
      return
    }
    typeof window !== 'undefined' && window.history.back()
    // navigate(-1)
  }
  return (
    <Button variant={variant} size={size} isIconOnly={isIconOnly} onPress={handler}>
      <MoveLeftIcon /> {label}
    </Button>
  )
}
