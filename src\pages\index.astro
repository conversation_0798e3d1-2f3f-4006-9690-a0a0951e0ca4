---
import Layout from '@/layouts/Layout.astro'
import Categories from '../components/Categories.astro'
import { FreeShippingAlert } from '@/components/react/FreeShippingAlert'
import { trpc } from '@/trpc'
import { COMPANY_NAME, COMPANY_EMAIL, COMPANY_PHONES, COMPANY_WORKING_HOURS, getRtiImageUrl } from '@/lib/config'
import { HtmlEditorButton } from '@components/react/HtmlEditorButton'

const [categories, htmlHeader, htmlAbout] = await Promise.all([
  trpc.products.getRootCategories.query(),
  trpc.services.getHtmkChunk.query({ id: 2 }),
  trpc.services.getHtmkChunk.query({ id: 3 })
])

// SEO данные
const currentUrl = Astro.url.href
const title = `${COMPANY_NAME} - крупнейший в России специализированный магазин сальников (oilseals) и уплотнений. Каталог сальников, манжет, уплотнениий. Продажа оптом и в розницу`
const description =
  'Продажа сальников, манжет и других резинотехнических изделий (РТИ). Широкий ассортимент, доставка по России, лучшие цены для оптовых и розничных покупателей.'
const mainImage = getRtiImageUrl('og-image.jpg')

// Кеширование
Astro.response.headers.set('Cache-Control', 'public, max-age=7200, stale-while-revalidate=7200')
// Добавляем заголовок Vary для правильного кеширования разных вариантов страницы
Astro.response.headers.set('Vary', 'Accept, Cookie, Accept-Encoding, Accept-Language, User-Agent')

const isAdmin = Astro.locals.isAdmin
---

<Layout title={title} description={description} image={mainImage}>
  <!-- Основные мета-теги -->
  <link rel='canonical' href={currentUrl} />
  <meta
    name='keywords'
    content='сальники, манжеты, РТИ, уплотнения, резинотехнические изделия, купить сальники оптом, манжеты купить, oilseal, oil seal, oilseals, seals, каталог сальников, сальники по размерам, подбор сальника'
  />
  <meta name='robots' content='index, follow' />

  <!-- Open Graph расширенные -->
  <meta property='og:type' content='website' />
  <meta property='og:url' content={currentUrl} />
  <meta property='og:title' content={title} />
  <meta property='og:description' content={description} />
  <meta property='og:image' content={mainImage} />
  <meta property='og:site_name' content={COMPANY_NAME} />

  <!-- Для мессенджеров -->
  <meta property='og:image:width' content='1200' />
  <meta property='og:image:height' content='630' />
  <meta property='og:image:alt' content={`${COMPANY_NAME} - Главная страница`} />

  <!-- Schema.org разметка -->
  <script
    type='application/ld+json'
    set:html={JSON.stringify({
      '@context': 'https://schema.org',
      '@type': 'Organization',
      'name': COMPANY_NAME,
      'url': currentUrl,
      'logo': mainImage,
      'description': description,
      'contactPoint': [
        {
          '@type': 'ContactPoint',
          'telephone': COMPANY_PHONES.toll_free,
          'contactType': 'sales',
          'availableLanguage': 'Russian',
          'areaServed': 'RU'
        },
        {
          '@type': 'ContactPoint',
          'telephone': COMPANY_PHONES.mobile,
          'contactType': 'sales',
          'availableLanguage': 'Russian',
          'areaServed': 'RU'
        }
      ],
      'email': COMPANY_EMAIL,
      'openingHoursSpecification': {
        '@type': 'OpeningHoursSpecification',
        'dayOfWeek': COMPANY_WORKING_HOURS.workDays,
        'opens': COMPANY_WORKING_HOURS.opens,
        'closes': COMPANY_WORKING_HOURS.closes
      }
    })}
  />

  <main class='w-full'>
    <FreeShippingAlert client:only='react' />
    <div class='container mx-auto flex justify-center'>
      {
        isAdmin && (
          <div class='mb-2'>
            <HtmlEditorButton size='sm' label=' ' chunkId={2} client:only='react' />
          </div>
        )
      }
      <div set:html={htmlHeader?.body} />
    </div>
    <Categories categories={categories} />
    <div class='container mx-auto flex justify-center'>
      {
        isAdmin && (
          <div class='mb-2'>
            <HtmlEditorButton size='sm' label=' ' chunkId={3} client:only='react' />
          </div>
        )
      }
      <div set:html={htmlAbout?.body} />
    </div>
  </main>
</Layout>
