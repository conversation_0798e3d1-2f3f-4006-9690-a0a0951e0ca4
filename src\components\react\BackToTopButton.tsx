import { useState, useEffect, useCallback, useMemo } from 'react'
import { But<PERSON> } from "@heroui/button"
import { ArrowUp } from 'lucide-react'
import debounce from 'lodash/debounce'

export const BackToTopButton = () => {
  const [isVisible, setIsVisible] = useState(false)

  const scrollToTop = useCallback(() => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    })
  }, [])

  useEffect(() => {
    const toggleVisibility = debounce(() => {
      const currentScrollPos = window.scrollY
      if (currentScrollPos > 50) {
        setIsVisible(true)
      } else {
        setIsVisible(false)
      }
    }, 100)

    window.addEventListener('scroll', toggleVisibility)

    return () => window.removeEventListener('scroll', toggleVisibility)
  }, [])

  const computedClassName = useMemo(() =>
    `bg-default-800 text-white fixed bottom-14 right-4 z-50 rounded-full shadow-lg transition-opacity duration-300 ${
      isVisible ? 'opacity-100' : 'opacity-0 pointer-events-none'
    }`,
    [isVisible]
  )

  return (
    <Button
      isIconOnly
      color='default'
      aria-label='Вернуться к началу страницы'
      className={computedClassName}
      onPress={scrollToTop}
      title="Вернуться к началу страницы"
      role="button"
    >
      <ArrowUp className='h-6 w-6' aria-hidden="true" />
    </Button>
  )
}
