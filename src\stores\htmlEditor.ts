import { atom } from 'nanostores'

// Интерфейс для состояния редактора товаров
interface chunkEditorState {
    isOpen: boolean
    chunkId?: string | null
    chunkKey?: string | null
}

// Создаем атом с начальным состоянием
export const $chunkEditor = atom<chunkEditorState>({
    isOpen: false,
    chunkId: null,
    chunkKey: null
})

// Функция для открытия модального окна редактора
export function openchunkEditor({ chunkId, chunkKey }: { chunkId?: number, chunkKey?: string }) {
    $chunkEditor.set({
        isOpen: true,
        chunkId,
        chunkKey
    })
}

// Функция для закрытия модального окна редактора
export function closechunkEditor() {
    $chunkEditor.set({
        isOpen: false,
        chunkId: null,
        chunkKey: null
    })
}
