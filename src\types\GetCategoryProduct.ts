import type { trpc } from "@/trpc"

export type CategoryData = Awaited<ReturnType<typeof trpc.products.getCategoryProduct.query>>

export  type Category = CategoryData['category']
export  type CategoryColumn = Category['columns'][0]
export  type CategoryProductsMeta = CategoryData['products']['meta']
export  type CategoryProductsData = CategoryData['products']['data']
export  type CategoryProduct = CategoryProductsData[0]
