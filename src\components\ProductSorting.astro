---
import { CSorting } from '@/components/react/CSorting'
import type { Category } from '@/types/GetCategoryProduct'

interface Props {
  initialSorting?: any[]
  isMobile?: boolean
  columns?: Category['columns']
  label?: string
}

const { initialSorting = [], isMobile = false, columns, label } = Astro.props

// const sortOptions = [
//   { value: 'prod_price:asc', label: 'Цена по возрастанию' },
//   { value: 'prod_price:desc', label: 'Цена по убыванию' },
//   { value: 'prod_count:asc', label: 'Наличие по возрастанию' },
//   { value: 'prod_count:desc', label: 'Наличие по убыванию' }
// ]
---

<CSorting label={label} columns={columns} client:visible initialSorting={initialSorting} isMobile={isMobile}></CSorting>
