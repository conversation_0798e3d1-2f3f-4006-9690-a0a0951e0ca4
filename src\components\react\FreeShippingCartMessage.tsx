import { useFreeShipping } from '@/lib/freeShipping'
import { Alert } from '@heroui/react'

export const FreeShippingCartMessage = () => {
  const { isEligible, remainingAmount, config } = useFreeShipping()

  if (!config?.isActive || isEligible) {
    return null
  }

  if (remainingAmount <= 0) {
    return null
  }

  return (
    <div className="w-full my-3">
      <Alert
        classNames={{
            title: 'md:text-base',
            description: 'md:text-sm'
        }}
        color="success"
        variant="flat"
        // title="Бесплатная доставка!"
        title={`Добавьте ещё товаров на ${remainingAmount.toLocaleString('ru-RU')} ₽ для бесплатной доставки.`}
        className="w-full"
      />
    </div>
  )
}