import { memo } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>er<PERSON><PERSON>er, useDisclosure } from '@heroui/react'
import { EyeIcon } from 'lucide-react'
import { ProductQuickView } from './ProductQuickView'

interface QuickViewButtonProps {
  product: any
  size?: 'sm' | 'md' | 'lg'
}

export const QuickViewButton = memo(({ product, size = 'sm' }: QuickViewButtonProps) => {
  const { isOpen, onOpen, onOpenChange, onClose } = useDisclosure()

  return (
    <>
      <Button
        size={size}
        className="flex justify-center gap-2 rounded-lg bg-default-100 p-2 text-center text-sm"
        variant="flat"
        onPress={onOpen}
        aria-label={`Быстрый просмотр товара: ${product.prod_purpose || product.prod_sku || 'товар'}`}
      >
        <span className="text-xs text-default-900">Быстрый <br/> Просмотр</span>
        <EyeIcon className="w-5" aria-hidden="true" />
      </Button>

      <Drawer
        isOpen={isOpen}
        onOpenChange={onOpenChange}
        placement="bottom"
        size="3xl"
        backdrop="opaque"
        aria-labelledby="quick-view-drawer-header"
        classNames={{
          base: "rounded-t-xl p-0",
          body: "p-6"
        }}
      >
        <DrawerContent>
          {(onClose) => (
            <>
              <DrawerHeader id="quick-view-drawer-header" className="border-b border-default-200">
                Быстрый просмотр товара
              </DrawerHeader>
              <DrawerBody>
                <ProductQuickView product={product} onClose={onClose} />
              </DrawerBody>
              <DrawerFooter className="border-t border-default-200">
                <Button
                  color="danger"
                  variant="light"
                  onPress={onClose}
                  aria-label="Закрыть быстрый просмотр"
                >
                  Закрыть
                </Button>
              </DrawerFooter>
            </>
          )}
        </DrawerContent>
      </Drawer>
    </>
  )
})

QuickViewButton.displayName = 'QuickViewButton'
