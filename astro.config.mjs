// @ts-check
import { defineConfig } from 'astro/config'
import tailwind from '@astrojs/tailwind'
// import tailwindcss from '@tailwindcss/vite'; //v4
import react from '@astrojs/react'
// import purgecss from 'astro-purgecss'
import node from '@astrojs/node'
import playformCompress from '@playform/compress'
// import MillionLint from '@million/lint'

const proxyTarget = import.meta.env.DEV ? import.meta.env.VITE_API_URL_DEV : import.meta.env.VITE_API_URL

export default defineConfig({
  devToolbar: {
    enabled: false
  },
  integrations: [
    tailwind({ applyBaseStyles: false }),
    react(),
    playformCompress(),
    // MillionLint.astro({
    //   optimizeDOM: true,
    //   telemetry: false,
    //   react: '19',
    //   skipTransform: true,
    //   rsc: true,
    //   dev: import.meta.env.DEV
    // })
  ],
  compressHTML: true,
  // purgecss()

  build: {
    inlineStylesheets: 'auto'
  },

  vite: {
    plugins: [
      // tailwindcss() // v4
    ],
    build: {
      rollupOptions: {
        output: {
          manualChunks: {
            // Split vendor chunks
            vendor: ['react', 'react-dom'],
            ui: ['@heroui/react'],
            // Добавляем отдельный чанк для поиска
            search: ['@/components/react/InstantSearchBox', '@/components/react/SearchBar'],
            // Основные библиотеки
            // Разделение тяжелых библиотек
            motion: ['framer-motion'],
            tanstack: ['@tanstack/react-table', '@tanstack/react-virtual', '@tanstack/react-query'],
            // Компоненты поиска
            // Компоненты таблиц и карточек
            product: ['@/components/react/ProductList', '@/components/react/ProductsCards'],
            navbar: ['@/components/react/NavbarMain.tsx']
          }
        }
      },
      cssCodeSplit: true,
      modulePreload: true,
      minify: 'esbuild',
      // terserOptions: {
      //   compress: {
      //     drop_console: true,
      //     passes: 2,
      //     ecma: 2020,
      //     module: true
      //   },
      //   format: {
      //     comments: false
      //   }
      // }
    },
    server: {
      proxy: {
        '/trpc': {
          target: proxyTarget,
          changeOrigin: true
          // rewrite: (path) => path.replace(/^\/api/, '')
        },
        '/api': {
          target: proxyTarget,
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, '')
        }
      }
    }
  },

  image: {
    service: {
      entrypoint: 'astro/assets/services/sharp',
      config: {
        formats: ['webp', 'avif'],
        quality: 80,
        densities: [1, 2],
        minimumCacheTTL: 60 * 60 * 24 * 7 // 7 дней
      }
    }
  },

  output: 'server',
  adapter: node({
    mode: 'standalone'
  }),

  experimental: {
    clientPrerender: true
  },

  prefetch: {
    prefetchAll: false,
    defaultStrategy: 'hover'
  }
})
