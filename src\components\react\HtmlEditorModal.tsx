import { memo, useCallback, useMemo } from 'react'
import { <PERSON>dal, ModalContent, ModalHeader, ModalBody, ModalFooter, But<PERSON> } from '@heroui/react'
import { useStore } from '@nanostores/react'
import { $chunkEditor, closechunkEditor } from '@/stores/htmlEditor'

export const HtmlEditorModal = memo(() => {
    const { isOpen, chunkId, chunkKey } = useStore($chunkEditor)

    const handleClose = useCallback(() => {
        closechunkEditor()
        window.location.reload()
    }, [])

    const qs = useMemo(() => {
        return new URLSearchParams({
            id: chunkId || '',
            key: chunkKey || ''
        }).toString()
    }, [chunkId, chunkKey])

    return (
        <Modal
            isOpen={isOpen}
            onClose={handleClose}
            size='full'
            scrollBehavior='outside'
            backdrop='blur'
            classNames={{
                base: 'bg-background/80',
                body: 'p-0'
            }}
        >
            <ModalContent>
                {(onClose) => (
                    <>
                        <ModalHeader className='flex items-center justify-between'>
                            <div className='text-base'>Редактировать {chunkKey}</div>
                        </ModalHeader>
                        <ModalBody className='p-0'>
                            <div className='mt-2'>
                                {chunkId && (
                                    <iframe
                                        title='Редактировать'
                                        allowFullScreen
                                        style={{ border: 'none' }}
                                        width='100%'
                                        height='800vh'
                                        src={`https://a.mirsalnikov.ru/settings/chunk?${qs}`}
                                        id='html-editor-iframe'
                                    />
                                )}
                            </div>
                        </ModalBody>
                    </>
                )}
            </ModalContent>
        </Modal>
    )
})

HtmlEditorModal.displayName = 'HtmlEditorModal'
