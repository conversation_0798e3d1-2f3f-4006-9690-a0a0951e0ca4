import { useEffect } from 'react'
import { sendGAPageView, sendYMPageView } from '@/lib/analytics'

/**
 * Компонент для отслеживания событий аналитики
 * Используется для отслеживания переходов между страницами в React-компонентах
 */
export function AnalyticsTracker() {
  useEffect(() => {
    // Отправляем событие просмотра страницы при монтировании компонента
    const currentPath = window.location.pathname
    sendGAPageView(currentPath)
    sendYMPageView(currentPath)

    // Отслеживаем переходы между страницами
    const handlePageView = () => {
      const path = window.location.pathname
      sendGAPageView(path)
      sendYMPageView(path)
    }

    // Подписываемся на событие перехода между страницами в Astro
    document.addEventListener('astro:page-load', handlePageView)

    // Отписываемся при размонтировании компонента
    return () => {
      document.removeEventListener('astro:page-load', handlePageView)
    }
  }, [])

  // Компонент не рендерит ничего в DOM
  return null
}

export default AnalyticsTracker
