import { defineMiddleware } from 'astro:middleware'
import { U<PERSON>ars<PERSON> } from 'ua-parser-js'
import { isMobile } from '@/stores/isMobile'

export const onRequest = defineMiddleware(async (context, next) => {
  // //console.log('mdlwr: ', {cookie: context.cookies, url: context.url});

  // //console.log('context.cookies _cart', context.cookies.get('_cart'))

  const userAgent = context.request.headers.get('user-agent') || ''
  const parser = new UAParser(userAgent)

  const initialIsMobile = parser.getDevice().type === 'mobile'
  context.locals.isMobile = initialIsMobile

  context.locals.isIOS = parser.getOS().is('iOS')

  const viewMode = context.cookies.get('view-mode')?.value
  context.locals.viewMode = initialIsMobile ? 'grid' : viewMode ? (viewMode === 'grid' ? 'grid' : 'table') : 'table'
  // Добавляем обработку режима поиска
  const searchMode = context.cookies.get('search-mode')?.value as 'flat' | 'category' | undefined
  context.locals.searchMode = searchMode || 'flat'

  context.locals.isAdmin = import.meta.env.DEV
  const ctoken = context.cookies.get('ctoken')?.value

  // console.log("🚀 ~ onRequest ~ ctoken:", ctoken)

  if (ctoken) {
    try {
      const res = await fetch(`${context.url.origin}/api/cpan/auth/check`, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${ctoken}`
        }
      })

      if (res.ok) {
        const data = await res.json()
        // console.log('🚀 ~ isAdmin onRequest ~ data:', data)
        if (data?.user_id) {
          context.locals.isAdmin = true
        }
      }
    } catch (error) {
      // Ошибка проверки администратора, оставляем isAdmin = false
      console.error('Error checking admin status:', error)
    }
  }

  isMobile.set(initialIsMobile)

  return next()
})
