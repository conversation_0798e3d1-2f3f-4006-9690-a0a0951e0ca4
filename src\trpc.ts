import type { AppRouter } from '../../rti-api/app/Services/tRPC'

import { createTRPCClient, httpBatchLink } from '@trpc/client'
import { createTRPCReact } from '@trpc/react-query'
import { createTRPCContext } from '@trpc/tanstack-react-query'

const ONE_DAY_IN_SECONDS = 60 * 60 * 24

// Determine if in development mode and if running in the browser
const isDev = import.meta.env.DEV
const isBrowser = typeof window !== 'undefined'

// Set the base URL for API requests
// const baseUrl = (isDev ? (isBrowser ? '' : `${import.meta.env.PUBLIC_API_URL_DEV}`) : import.meta.env.PUBLIC_API_URL) + '/trpc'

const baseUrl = isBrowser ? '/api/trpc' : `${import.meta.env.PUBLIC_API_LOCAL_URL}/trpc`

export const trpcEndPoint = `${baseUrl}`
//console.log('trpcEndPoint:', trpcEndPoint)

export const trpcReact = createTRPCReact<AppRouter>({
  abortOnUnmount: true
})

// Utility function to extract cookies from the request context
// const extractCookies = (req: Request) => {
//   const cookiesHeader =  req.headers.get('cookie') || '';
//   return cookiesHeader
// }

// Create the tRPC client with updated fetch function
export const trpc = createTRPCClient<AppRouter>({
  links: [
    httpBatchLink({
      url: baseUrl,
      fetch(url, options) {
        //console.log('url:', url)

        return fetch(url, {
          ...options,
          credentials: 'include',
          // headers: {
          //   ...options?.headers,
          // },
          headers: {
            ...options?.headers,
            'Cache-Control': 'max-age=1800'
          },
          keepalive: true
        })
      }
    })
  ]
})

export const useSSRtRPC = ({ cookieHeader }) => {
  console.log('useSSRtRPC cookieHeader:', cookieHeader)

  return createTRPCClient<AppRouter>({
    links: [
      httpBatchLink({
        url: baseUrl,
        fetch(url, options) {
          return fetch(url, {
            ...options,
            credentials: 'include',
            headers: {
              ...options?.headers,
              cookie: cookieHeader
            }
          })
        }
      })
    ]
  })
}

export const { TRPCProvider, useTRPC } = createTRPCContext<AppRouter>()
