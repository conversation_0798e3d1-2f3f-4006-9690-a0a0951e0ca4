
// // 10574 - 1123


import { Button, Input, Form, Alert } from '@heroui/react'
import { navigate } from 'astro:transitions/client'
import { ArrowLeftIcon, EyeIcon, EyeOffIcon, LockIcon } from 'lucide-react'
import { useState } from 'react'
import { setUser } from '@/stores/user'

interface LoginWidgetProps {
  onToReg?: () => void
  showLoginWidget?: boolean
}

export const LoginWidget = ({ onToReg, showLoginWidget }: LoginWidgetProps) => {
  const [isVisible, setIsVisible] = useState(false)
  const [loading, setLoading] = useState(false)

  // Состояния формы
  const [login, setLogin] = useState('')
  const [password, setPassword] = useState('')
  const [email, setEmail] = useState('')
  const [confirmCode, setConfirmCode] = useState('')

  // Состояния режимов
  const [setPasswordMode, setSetPasswordMode] = useState(false)
  const [resetPasswordMode, setResetPasswordMode] = useState(false)
  const [codeMode, setCodeMode] = useState(false)

  // Сообщения
  const [errorMessage, setErrorMessage] = useState('')
  const [successMessage, setSuccessMessage] = useState('')
  const [resetSuccessMessage, setResetSuccessMessage] = useState('')

  const clearErrorMessage = () => {
    if (password.length >= 4) {
      setErrorMessage('')
    }
  }

  const resetPwd = async () => {
    setLoading(true)

    if (email.length > 2) {
      try {
        const response = await fetch(`/api/auth/login/resetpassword/${encodeURIComponent(email)}`)

        const data = await response.json()

        setResetSuccessMessage(data.msg || data.error)

        if (data.msg) {
          setEmail('')
        }

        setTimeout(() => {
          setResetSuccessMessage('')
          setResetPasswordMode(false)
        }, 3000)
      } catch (error) {
        setResetSuccessMessage('Ошибка при сбросе пароля')
      }
    } else {
      setResetSuccessMessage('Введите ваш E-mail')
    }

    setLoading(false)
  }

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    setErrorMessage('')
    setLoading(true)

    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ login, password }),
        credentials: 'include'
      })

      const data = await response.json()

      if (data.emptypassword) {
        setSetPasswordMode(true)
      } else if (data.client_id) {
        setUser({
          client_id: data.client_id,
          email: data.email,
          name: data.name,
          isAuthenticated: true
        })
        navigate('/dashboard/orders')
        window.location.reload()
      } else {
        setErrorMessage(data.error || 'Ошибка входа')
      }
    } catch (error) {
      setErrorMessage('Пользователь не найден')
    } finally {
      setLoading(false)
    }
  }

  const setNewPassword = async () => {
    if (password.length < 4) {
      setErrorMessage('Пароль должен состоять минимум из 4 символов')
      return
    }

    try {
      const response = await fetch('/api/auth/setpassword', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          login,
          password,
          confirmCode
        }),
        credentials: 'include'
      })

      const data = await response.json()

      if (data.error) {
        setErrorMessage(data.error)
        return
      }

      if (data.requestcode) {
        setCodeMode(true)
      } else {
        setSuccessMessage(data.msg)
        setTimeout(() => {
          setSuccessMessage('')
          setConfirmCode('')
          setSetPasswordMode(false)
          setCodeMode(false)
        }, 3000)
      }
    } catch (error) {
      setErrorMessage('Ошибка при установке пароля')
    }
  }

  return (
    <div className='flex h-full w-full items-center justify-center'>
      <div className='flex w-full max-w-sm flex-col gap-4 rounded-large px-2 pb-10 pt-6'>
        {resetPasswordMode ? (
          <>
            <p className='text-lg text-gray-700'>Сброс пароля</p>
            <Input autoFocus label='Ваш E-mail или номер клиента' value={email} onChange={(e) => setEmail(e.target.value)} type='text' />
            {resetSuccessMessage && <Alert>{resetSuccessMessage}</Alert>}
            <Button className='w-full' isLoading={loading} color='warning' onPress={resetPwd}>
              Продолжить
            </Button>
            <Button variant='ghost' color='default' onPress={() => setResetPasswordMode(false)}>
              <ArrowLeftIcon/> Назад
            </Button>
          </>
        ) : !setPasswordMode ? (
          <>
            <Alert>Регистрация не требуется. Учетная запись будет создана после первого заказа</Alert>
            <Form onSubmit={handleLogin}>
              <Input autoFocus isRequired label='Номер клиента или E-mail' value={login} onChange={(e) => setLogin(e.target.value)} type='text' />
              <Input
                isRequired
                label='Пароль'
                value={password}
                onChange={(e) => {
                  setPassword(e.target.value)
                  clearErrorMessage()
                }}
                type={isVisible ? 'text' : 'password'}
                endContent={
                  <Button type='button' onPress={() => setIsVisible(!isVisible)}>
                    {isVisible ? <EyeOffIcon className='text-2xl text-default-500' /> : <EyeIcon className='text-2xl text-default-500' />}
                  </Button>
                }
              />
              {errorMessage && <Alert color='danger'>{errorMessage}</Alert>}
              <Button className='w-full' color='warning' type='submit' isLoading={loading}>
                Войти
              </Button>
            </Form>
            <div className='flex justify-between'>
              <Button color='default' onPress={() => setResetPasswordMode(true)} startContent={<LockIcon />}>
                Восстановить пароль
              </Button>
            </div>
          </>
        ) : (
          <>
            {successMessage ? (
              <p className='text-lg font-bold text-gray-700'>{successMessage}</p>
            ) : (
              <>
                {codeMode ? (
                  <>
                    <p className='font-bold text-blue-500'>Код подтверждения из E-mail</p>
                    <Input
                      autoFocus
                      label='Код подтверждения'
                      value={confirmCode}
                      onChange={(e) => setConfirmCode(e.target.value)}
                      errorMessage={errorMessage}
                    />
                  </>
                ) : (
                  <>
                    <p className='font-bold text-blue-500'>Пожалуйста, задайте пароль для вашего кабинета</p>
                    <Input
                      autoFocus
                      label='Пароль'
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      errorMessage={errorMessage}
                      type='password'
                    />
                  </>
                )}
                <Button className='w-full' color='warning' onPress={setNewPassword}>
                  Продолжить
                </Button>
              </>
            )}
          </>
        )}
      </div>
    </div>
  )
}
