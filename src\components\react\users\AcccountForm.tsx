import { Input } from '@heroui/react'
import type { FormComponentProps } from '@/types/form'

export const AccountForm = ({ data, onChange }: FormComponentProps) => (
    <div className='flex flex-wrap w-full gap-4'>
        <Input
            isRequired
            name='email'
            label='Email'
            type='email'
            placeholder='<EMAIL>'
            value={data.email}
            onChange={onChange}
            validationBehavior='native'
            errorMessage='Введите корректный email'
        />
        <Input
            isRequired
            name='password'
            label='Пароль'
            type='password'
            placeholder='Минимум 8 символов'
            value={data.password}
            onChange={onChange}
            validationBehavior='native'
            pattern='.{8,}'
            errorMessage='Пароль должен содержать минимум 8 символов'
        />
    </div>
) 