/* Переопределение стилей HeroUI для отключения обводки при фокусе */

/* Глобальное отключение обводки при фокусе */
*:focus {
  outline: none !important;
  box-shadow: none !important;
}

/* Отключение обводки для компонентов HeroUI */
/* .nextui-input:focus,
.nextui-input-wrapper:focus-within,
.nextui-button:focus,
.nextui-select:focus,
.nextui-select-wrapper:focus-within,
.nextui-dropdown-trigger:focus,
.nextui-tabs-tab:focus,
.nextui-checkbox:focus,
.nextui-radio:focus,
.nextui-switch:focus,
.nextui-link:focus,
.nextui-card:focus,
.nextui-popover-trigger:focus,
.nextui-tooltip-trigger:focus,
.nextui-modal-trigger:focus,
.nextui-drawer-trigger:focus {
  outline: none !important;
  box-shadow: none !important;
  border-color: inherit !important;
} */

/* Отключение обводки для нативных элементов */
input:focus,
textarea:focus,
select:focus,
button:focus,
a:focus,
[role="button"]:focus,
[tabindex]:focus {
  outline: none !important;
  box-shadow: none !important;
  border-color: inherit !important;
}

/* Отключение обводки для элементов с фокусом внутри */
*:focus-within {
  outline: none !important;
  box-shadow: none !important;
}
