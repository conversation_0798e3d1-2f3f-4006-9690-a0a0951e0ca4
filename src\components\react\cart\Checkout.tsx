import React, { useRef, useEffect, useState, useMemo } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { CheckIcon, HomeIcon, AlertTriangleIcon, XCircleIcon, XIcon } from 'lucide-react'
import { addToast, Alert, Button, Textarea, Card, CardBody, Spinner } from '@heroui/react'
import { PersonalForm } from '../users/PersonalForm'
import { AddressForm } from '../users/AddressForm'
import { CartSum } from './CartSum'
import { PaymentMethods } from './PaymentMethods'
import { ShippingMethods } from './ShippingMethods'
import type { ShippingMethodType } from '@/types'
import type { Country } from '@/types/form'
import { useStore } from '@tanstack/react-store'
import { cartIsLoading, cartItems, cartSums, cartTotal, setCartIsLoading } from '@/stores/cart'
import { useLocalStorage } from '@/hooks/useLocalStorage'
import { navigate } from 'astro:transitions/client'
import { priceFormat } from '@/lib/priceFormat'

interface OrderRecipient {
  // Добавим логирование всех полей
  firstName: string
  lastName: string
  phone: string
  isLegalEntity: boolean
  companyName: string
  inn: string
  kpp: string
  ogrn: string
  bank?: string
  bik?: string
  rschet?: string
  kschet?: string
  fullName: string
  email: string
}

interface Address {
  country: string
  countryData: {
    iso: number
    country_id: number
  }
  location: string
  locationData: unknown
  street: string
  house: string
  apartment: string
  zipCode: string
}

interface OrderFormData {
  address: {
    country: string
    location: string
    street: string
    house: string
    flat: string
    index: string
  }
  client: {
    name: string
    email: string
    phone: string
    cdekId: string
    org: {
      name: string
      address: string
      inn: string
      kpp: string
      rschet: string
      kschet: string
      bik: string
      bank: string
      meta: string
      vat: string
    }
    passport: {
      series: string
      number: string
      issueDate: string
      issuedBy: string
      birthDate: string
    }
  }
  shipping: {
    method: string
    methodName: string
    price: number
  }
  payment: {
    method: string
    methodName: string
  }
  notice: string
}

const defaultApiSettings = {
  'pochta.standard.maxValue': 20000
  // 'shipping.ems.active': true,
  // 'shipping.cdek.active': true
}

const STORAGE_KEY = 'checkout_form_data'

const transformOrderData = (orderData): OrderFormData => {
  return {
    address: {
      country: orderData.address.country,
      location: orderData.address.location,
      street: orderData.address.street,
      house: orderData.address.house,
      flat: orderData.address.apartment,
      index: orderData.address.zipCode
    },
    client: {
      name: orderData.recipient.fullName, // Используем fullName напрямую
      email: orderData.recipient.email,
      phone: orderData.recipient.phone,

      cdekId: '',
      org: {
        name: orderData.recipient.companyName,
        address: orderData.recipient.address, // Используем новое поле адреса
        inn: orderData.recipient.inn,
        kpp: orderData.recipient.kpp,
        rschet: orderData.recipient.rschet || '',
        kschet: orderData.recipient.kschet || '',
        bik: orderData.recipient.bik || '',
        bank: orderData.recipient.bank || '',
        meta: '',
        vat: ''
      },
      passport: {
        series: '',
        number: '',
        issueDate: '',
        issuedBy: '',
        birthDate: ''
      }
    },
    shipping: {
      shortName: orderData.shipping.shortTitle,
      method: orderData.shipping.shortTitle,
      methodName: orderData.shipping.method,
      price: orderData.shipping.price
    },
    payment: {
      method: orderData.payment.title,
      methodName: orderData.payment.method
    },
    notice: orderData.notice
  }
}

interface ClientData {
  client_id: number
  client_number: number
  client_name: string
  client_mail: string
  client_phone: string
  client_country: string
  client_city: string
  client_street: string
  client_house: string
  client_flat: string
  client_postindex: string
  client_discount: number
  client_cdekid: string
}

// Интерфейс для элементов ошибок и предупреждений
interface CartItemError {
  sku: string
  query: number
  stock: number
}

export const Checkout = () => {
  // Текущий шаг оформления
  const [currentStep, setCurrentStep] = useState(0)

  const [isLoading, setIsLoading] = useState(false)
  const [cartErrors, setCartErrors] = useState<CartItemError[]>([])
  const [stockWarns, setStockWarns] = useState<CartItemError[]>([])
  const [dismissedErrors, setDismissedErrors] = useState<string[]>([])
  const [dismissedWarnings, setDismissedWarnings] = useState<string[]>([])

  const $cartSums = useStore(cartSums)
  const $cartTotal = useStore(cartTotal)
  const $cartIsLoading = useStore(cartIsLoading)

  // Обновляем начальное состояние
  const initialOrderData = {
    recipient: {
      fullName: '', // Оставляем только fullName
      phone: '',
      isLegalEntity: false,
      companyName: '',
      address: '', // Добавляем поле адреса
      inn: '',
      kpp: '',
      ogrn: '',
      bank: '',
      bik: '',
      rschet: '',
      kschet: '',
      email: ''
    },
    address: {
      country: 'Россия',
      countryData: {
        iso: 643,
        country_id: 643,
        title: 'Россия'
      },
      location: '',
      locationData: null,
      street: '',
      house: '',
      apartment: '',
      zipCode: ''
    },
    payment: {
      method: ''
    },
    shipping: {
      method: '',
      price: 0,
      notice: ''
    },
    notice: ''
  }

  const [isAuthorized, setIsAuthorized] = useState(false)
  const [clientData, setClientData] = useState<ClientData | null>(null)

  // Эффект для проверки авторизации и загрузки данных пользователя
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const response = await fetch('/api/auth/login/check', {
          credentials: 'include'
        })
        if (response.ok) {
          const data = await response.json()
          setIsAuthorized(true)
          setClientData(data)

          // Заполняем основные поля из профиля и юр.лица, если есть org
          setOrderData((prev) => ({
            ...prev,
            recipient: {
              ...prev.recipient,
              fullName: data.client_name || '',
              phone: data.client_phone || '',
              email: data.client_mail || '',
              isLegalEntity: !!data.org,
              companyName: data.org?.org_name || '',
              inn: data.org?.org_inn || '',
              kpp: data.org?.org_kpp || '',
              // ogrn: '', // если появится в API — добавить
              address: data.org?.org_adress || '',
              bank: data.org?.org_bank || '',
              bik: data.org?.org_bik || '',
              rschet: data.org?.org_rschet || '',
              kschet: data.org?.org_kschet || ''
            },
            address: {
              country: data.client_country || 'Россия',
              // countryData: {
              //   iso: 643,
              //   country_id: 643
              // },
              location: data.client_city || '',
              locationData: null,
              street: data.client_street || '',
              house: data.client_house || '',
              apartment: data.client_flat || '',
              zipCode: data.client_postindex || ''
            }
          }))
        }
      } catch (error) {
        console.error('Ошибка проверки авторизации:', error)
      }
    }

    checkAuth()
  }, [])

  // Меняем использование useLocalStorage
  const [orderData, setOrderData] = useLocalStorage(STORAGE_KEY, initialOrderData, {
    skipStorage: isAuthorized // Пропускаем сохранение в localStorage если пользователь авторизован
  })

  // useEffect(() => {
  //   console.log('@orderData:', orderData)
  // }, [orderData])

  // Добавляем новые состояния для автокомплита
  const [selectedOrg, setSelectedOrg] = React.useState<any>(null)
  const [selectedBank, setSelectedBank] = React.useState<any>(null)

  const [selectedShippingMethod, setSelectedShippingMethod] = useState<string>()
  const [shippingPrice, setShippingPrice] = useState<number>(0)

  const stableApiSettings = useMemo(() => defaultApiSettings, [])

  // Проверка валидности индекса
  const isValidZipCode = useMemo(() => {
    return /^\d{6}$/.test(orderData.address.zipCode || '')
  }, [orderData.address.zipCode])

  // Формируем параметры для расчета доставки
  const shippingParams = useMemo(() => {
    // Всегда передаем индекс, даже если он невалидный
    // ShippingMethods сам решит, показывать ли предупреждение
    return {
      destinationIndex: orderData.address.zipCode || '',
      countryId: orderData.address?.countryData?.iso,
      isValidZipCode // Добавляем флаг валидности
    }
  }, [orderData.address.zipCode, orderData.address?.countryData?.iso, isValidZipCode])

  // Сохраняем предыдущие значения индекса и страны
  const prevZipCodeRef = React.useRef(orderData.address.zipCode)
  const prevCountryIsoRef = React.useRef(orderData.address?.countryData?.iso)

  // Сбрасываем данные о доставке при изменении адреса
  useEffect(() => {
    // Проверяем, действительно ли изменились значения
    const zipCodeChanged = prevZipCodeRef.current !== orderData.address.zipCode
    const countryChanged = prevCountryIsoRef.current !== orderData.address?.countryData?.iso

    // Если индекс или страна действительно изменились, сбрасываем выбранный метод доставки и цену
    if (zipCodeChanged || countryChanged) {
      // Всегда сбрасываем данные о доставке при изменении индекса или страны
      setSelectedShippingMethod(undefined)
      setShippingPrice(0)
    }

    // Обновляем сохраненные значения после проверки изменений
    prevZipCodeRef.current = orderData.address.zipCode
    prevCountryIsoRef.current = orderData.address?.countryData?.iso
  }, [orderData.address.zipCode, orderData.address?.countryData?.iso, isValidZipCode])

  // useEffect(() => {
  //   //console.log('Checkout orderData changed:', orderData);
  // }, [orderData])

  // useEffect(() => {
  //   //console.log('Checkout selectedShippingMethod changed:', selectedShippingMethod);
  // }, [selectedShippingMethod])

  // useEffect(() => {
  //   //console.log('Checkout shippingPrice state changed:', shippingPrice);
  // }, [shippingPrice])

  const contentRef = useRef<HTMLDivElement>(null)

  // Обработчик перехода к следующему шагу или оформления заказа
  const [validationErrors, setValidationErrors] = React.useState<{ [key: string]: string[] }>({})

  // Добавляем состояние валидности форм
  const [formsValidity, setFormsValidity] = useState({
    recipient: false,
    address: false,
    shipping: false,
    payment: false
  })

  // Обработчик изменения валидности для каждого шага
  const handleValidityChange = (step: keyof typeof formsValidity, isValid: boolean) => {
    setFormsValidity((prev) => ({
      ...prev,
      [step]: isValid
    }))
  }

  // Проверка валидности всех форм
  const checkFormsValidity = () => {
    const invalidStep = steps.findIndex((_, index) => {
      const stepKey = getStepKey(index)
      return stepKey && !formsValidity[stepKey]
    })

    if (invalidStep !== -1) {
      setCurrentStep(invalidStep)
      if (contentRef.current) {
        contentRef.current.scrollIntoView({ behavior: 'smooth', block: 'start' })
      }
      return false
    }
    return true
  }

  // Функция для получения ключа шага
  const getStepKey = (stepIndex: number): keyof typeof formsValidity | null => {
    const stepKeys: { [key: number]: keyof typeof formsValidity } = {
      0: 'recipient',
      1: 'address',
      2: 'shipping',
      3: 'payment'
    }
    return stepKeys[stepIndex] || null
  }

  // Загрузка данных из localStorage при монтировании
  useEffect(() => {
    const savedData = localStorage.getItem(STORAGE_KEY)
    if (savedData) {
      try {
        const parsedData = JSON.parse(savedData)
        if (parsedData.selectedOrg) setSelectedOrg(parsedData.selectedOrg)
        if (parsedData.selectedBank) setSelectedBank(parsedData.selectedBank)

        // Не восстанавливаем данные о доставке автоматически, чтобы избежать
        // преждевременного отображения стоимости доставки
        // Эти данные будут установлены только после явного выбора метода доставки
        // if (parsedData.selectedShippingMethod) setSelectedShippingMethod(parsedData.selectedShippingMethod)
        // if (parsedData.shippingPrice) setShippingPrice(parsedData.shippingPrice)
      } catch (e) {
        console.error('Error parsing saved checkout data:', e)
      }
    }
  }, [])

  // Очистка данных после успешного оформления заказа
  const handleOrderComplete = () => {
    localStorage.removeItem(STORAGE_KEY)
    // Сброс всех состояний
    setOrderData({
      recipient: {
        phone: '',
        isLegalEntity: false,
        companyName: '',
        inn: '',
        kpp: '',
        ogrn: '',
        fullName: '',
        email: ''
      },
      address: {
        country: '',
        countryData: {
          iso: 643,
          country_id: 643
        },
        location: '',
        locationData: null,
        street: '',
        house: '',
        apartment: '',
        zipCode: ''
      },
      payment: {
        method: ''
      },
      shipping: { method: '', price: 0, notice: '' },
      notice: ''
    })
    setSelectedOrg(null)
    setSelectedBank(null)
    setSelectedShippingMethod('')
    setShippingPrice(0)
  }

  // Модифицируем handleNext
  const handleNext = async () => {
    // console.log('[DEBUG handleNext]', { formsValidity, orderData });
    if (currentStep === steps.length - 1) {
      if (!checkFormsValidity()) {
        return
      }

      // Дополнительная проверка наличия метода доставки
      if (!orderData.shipping.method) {
        // Показываем уведомление о необходимости выбрать метод доставки
        addToast({
          color: 'danger',
          title: 'Ошибка оформления заказа',
          description: 'Пожалуйста, выберите метод доставки перед оформлением заказа',
          timeout: 5000,
          shouldShowTimeoutProgress: true
        })

        // Переходим к шагу выбора доставки
        setCurrentStep(2)
        setTimeout(() => {
          const shippingStep = document.querySelector(`[data-step="2"]`)
          if (shippingStep) {
            shippingStep.scrollIntoView({ behavior: 'smooth', block: 'start' })
          }
        }, 100)
        return
      }

      try {
        setIsLoading(true)
        // Transform data to API format
        const formData = transformOrderData(orderData)

        // Log the transformed data
        // console.log('Отправка заказа:', formData)

        const res = await fetch(`/api/orders/create`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(formData)
        })

        const data = await res.json()

        if (data?.errors || data?.stockWarns) {
          const hasErrors = data.errors && data.errors.length > 0
          const hasWarnings = data.stockWarns && data.stockWarns.length > 0

          setCartErrors(data.errors || [])
          setStockWarns(data.stockWarns || [])

          // Сбрасываем списки закрытых уведомлений при получении новых ошибок
          setDismissedErrors([])
          setDismissedWarnings([])

          // Показываем уведомление через addToast
          if (hasErrors) {
            addToast({
              color: 'danger',
              title: 'Ошибка в корзине',
              description: 'Некоторые товары отсутствуют в наличии и были удалены из корзины',
              timeout: 5000,
              shouldShowTimeoutProgress: true
            })
          }

          if (hasWarnings) {
            addToast({
              color: 'warning',
              title: 'Внимание',
              description: 'Количество некоторых товаров в корзине было изменено из-за ограничений по наличию',
              timeout: 5000,
              shouldShowTimeoutProgress: true
            })
          }

          // Скролл к блоку с ошибками и предупреждениями
          setTimeout(() => {
            const errorsBlock = document.querySelector('.cart-errors-block')
            if (errorsBlock) {
              errorsBlock.scrollIntoView({ behavior: 'smooth', block: 'start' })
            }
          }, 300)

          // Обновляем состояние корзины, чтобы вызвать обновление через подписку в CartProducts
          cartItems.setState((prev) => ({ ...prev }))
        }

        if (data.orderId) {
          cartItems.setState(() => ({}))

          const _qs = new URLSearchParams()

          // _qs.set('amount', String($shippingprice + $sumCart))
          // cart.fetchCart($page.url.search)

          try {
            data.paymentLink && _qs.set('paymentlink', data.paymentLink)
            data.confirmation_token && _qs.set('confirmation_token', data.confirmation_token)

            // orderData.client?.org?.name && _qs.set('adocs', 'true')
          } catch (error) {}

          // _qs.set('amount', ($cartSums.sum + orderData.shipping.price).toFixed(2))
          _qs.set('payment', orderData.payment.method)

          navigate(`/orderdone/${data.orderId}?${_qs.toString()}`)
        }
      } catch (error) {
        console.error('Ошибка при оформлении заказа:', error)
        // alert('Произошла ошибка при оформлении заказа')
        addToast({
          color: 'danger',
          title: 'Произошла ошибка при оформлении заказа. Попробуйте позже'
        })
      }
      setIsLoading(false)
    } else {
      setCurrentStep(currentStep + 1)
      // Добавляем прокрутку к следующему шагу
      setTimeout(() => {
        const nextStep = document.querySelector(`[data-step="${currentStep + 1}"]`)
        if (nextStep) {
          nextStep.scrollIntoView({ behavior: 'smooth', block: 'start' })
        }
      }, 100)
    }
  }

  // Обработчик возврата на предыдущий шаг
  const handlePrev = () => {
    if (currentStep > 0) setCurrentStep(currentStep - 1)
  }

  // Прокрутка контента при смене шага
  // useEffect(() => {
  //   if (contentRef.current) {
  //     contentRef.current.scrollIntoView({ behavior: 'smooth', block: 'start' })
  //   }
  // }, [currentStep])

  // Синхронизируем все состояния с localStorage
  useEffect(() => {
    if (!isAuthorized) {
      const dataToSave = {
        ...orderData,
        selectedOrg,
        selectedBank,
        // Сохраняем данные о доставке только если они были явно выбраны
        ...(selectedShippingMethod ? { selectedShippingMethod } : {}),
        ...(shippingPrice > 0 ? { shippingPrice } : {})
      }
      localStorage.setItem('checkout_form_data', JSON.stringify(dataToSave))
    }
  }, [orderData, selectedOrg, selectedBank, selectedShippingMethod, shippingPrice, isAuthorized])

  // Добавляем эффект для очистки неправильно сохраненных полей
  useEffect(() => {
    const legalEntityFields = ['companyName', 'inn', 'kpp', 'ogrn', 'bank', 'bik', 'rschet', 'kschet', 'address']
    if (legalEntityFields.some((field) => field in orderData)) {
      // console.log('Cleaning up incorrect field structure')
      const { selectedOrg, selectedBank, ...cleanData } = orderData
      setOrderData(cleanData)
    }
  }, [])

  // useEffect(() => {
  //   console.log('@@@@orderData changed:', orderData)
  //   console.log('@@@@orderData.address:', JSON.stringify(orderData.address, null, 2))
  // }, [orderData])

  // Функции для закрытия уведомлений
  const dismissError = (sku: string) => {
    setDismissedErrors((prev) => [...prev, sku])
  }

  const dismissWarning = (sku: string) => {
    setDismissedWarnings((prev) => [...prev, sku])
  }

  // Функция для закрытия всех уведомлений
  const dismissAllNotifications = () => {
    const allErrorSkus = cartErrors.map((item) => item.sku)
    const allWarningSkus = stockWarns.map((item) => item.sku)

    setDismissedErrors(allErrorSkus)
    setDismissedWarnings(allWarningSkus)

    addToast({
      color: 'success',
      title: 'Уведомления скрыты',
      // description: 'Все уведомления о проблемах с корзиной были скрыты',
      timeout: 3000
    })
  }

  // Фильтрация ошибок и предупреждений, которые не были закрыты
  const filteredErrors = cartErrors.filter((item) => !dismissedErrors.includes(item.sku))
  const filteredWarnings = stockWarns.filter((item) => !dismissedWarnings.includes(item.sku))

  // Эффект для скролла к блоку с ошибками при первой загрузке компонента
  useEffect(() => {
    if ((filteredErrors.length > 0 || filteredWarnings.length > 0) && !$cartIsLoading) {
      setTimeout(() => {
        const errorsBlock = document.querySelector('.cart-errors-block')
        if (errorsBlock) {
          errorsBlock.scrollIntoView({ behavior: 'smooth', block: 'start' })
        }
      }, 500)
    }
  }, [filteredErrors.length, filteredWarnings.length, $cartIsLoading])

  // Эффект для сброса метода доставки при изменении корзины
  useEffect(() => {
    // Если есть ошибки или предупреждения, значит корзина изменилась
    if (filteredErrors.length > 0 || filteredWarnings.length > 0) {
      // Сбрасываем метод доставки и цену
      setSelectedShippingMethod('')
      setShippingPrice(0)

      // Сбрасываем только shipping, не трогаем payment
      setOrderData((prev) => ({
        ...prev,
        shipping: {
          method: '',
          name: '',
          title: '',
          shortTitle: '',
          price: 0,
          note: '',
          icon: '',
          active: false,
          maxPrice: undefined
        }
      }))

      // Показываем уведомление о необходимости выбрать метод доставки заново
      addToast({
        color: 'warning',
        title: 'Метод доставки сброшен',
        description: 'Из-за изменений в корзине, пожалуйста, выберите метод доставки заново',
        timeout: 5000,
        shouldShowTimeoutProgress: true
      })
    }
  }, [filteredErrors.length, filteredWarnings.length])

  // Модифицируем обработчики изменения данных

  // Обновление данных получателя
  const handleRecipientChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    // console.log('Checkout handleRecipientChange:', { name, value })

    // Добавляем специальную обработку для данных организации и банка
    if (name === 'orgData' || name === 'bankData') {
      setOrderData((prev) => ({
        ...prev,
        recipient: {
          ...prev.recipient,
          ...value // value здесь уже объект с нужными полями
        }
      }))
    } else {
      setOrderData((prev) => ({
        ...prev,
        recipient: {
          ...prev.recipient,
          [name]: value
        }
      }))
    }
  }

  // Обновление данных адреса доставки
  const handleAddressChange = (e: { target: { name: string; value: any; isValid?: boolean } }) => {
    const { name, value } = e.target
    // console.log('Checkout handleAddressChange:', name, value)

    // Если value — объект с полями title и countryData, обновляем оба поля
    if (name === 'country' && value && typeof value === 'object' && value.countryData) {
      // console.log('Checkout: Обновление страны из автокомплита:', value)
      setOrderData((prev) => ({
        ...prev,
        address: {
          ...prev.address,
          country: value.title,
          countryData: value.countryData
        }
      }))
    } else if (name === 'addressPatch' && value && typeof value === 'object') {
      // Просто обновляем адрес без дополнительных проверок
      // Валидность индекса будет проверена через isValidZipCode в useEffect
      // console.log('addressPatch обновление адреса:', value)

      // Проверяем, есть ли locationData в value
      // if (value.locationData) {
      //   console.log('Checkout: Получен locationData в addressPatch:', value.locationData)
      // }

      // Обновляем адрес
      setOrderData((prev) => {
        // Создаем новый объект адреса с сохранением существующих данных
        const newAddress = { ...prev.address }

        // Сохраняем текущий индекс, чтобы проверить, изменился ли он
        const currentZipCode = newAddress.zipCode || ''

        // Сохраняем индекс из locationData, если он есть
        const locationIndex = newAddress.locationData?.index || ''

        // Явно обрабатываем поле location, если оно есть в value
        if ('location' in value) {
          newAddress.location = value.location
          // console.log('[DEBUG] Явно устанавливаем location:', value.location)
        }

        // Обновляем только те поля, которые пришли в value
        Object.keys(value).forEach((key) => {
          // Если это locationData, обрабатываем его
          if (key === 'locationData') {
            // Если locationData не null, сохраняем его
            if (value[key]) {
              newAddress[key] = value[key]

              // Если в новом locationData есть индекс и текущий индекс пустой, используем его
              if (value[key].index && !currentZipCode) {
                newAddress.zipCode = value[key].index
                // console.log('[DEBUG] Устанавливаем индекс из locationData:', value[key].index)
              }
            } else {
              // Если locationData равен null, но location содержит значение,
              // сохраняем locationData как null, но не трогаем поле location
              newAddress[key] = null
              // console.log('[DEBUG] locationData равен null, сохраняем его как null')
            }
          }
          // Если это streetData и он не null, сохраняем его
          else if (key === 'streetData' && value[key]) {
            newAddress[key] = value[key]

            // Если в streetData есть индекс, используем его
            if (value[key].index) {
              newAddress.zipCode = value[key].index
              // console.log('[DEBUG] Устанавливаем индекс из streetData:', value[key].index)
            }
            // Если в streetData нет индекса, но есть в locationData, используем его
            else if (locationIndex && value.zipCode === '') {
              newAddress.zipCode = locationIndex
              // console.log('[DEBUG] Восстанавливаем индекс из locationData:', locationIndex)
            }
          }
          // Если это zipCode и он пустой, но есть индекс в locationData, используем его
          else if (key === 'zipCode' && !value[key] && locationIndex) {
            newAddress[key] = locationIndex
            // console.log('[DEBUG] Используем индекс из locationData вместо пустого:', locationIndex)
          }
          // Для остальных полей просто обновляем значение
          else if (key !== 'location') {
            // Пропускаем location, так как мы уже обработали его выше
            newAddress[key] = value[key]
          }
        })

        // console.log('Checkout: Новый адрес после обновления:', newAddress)

        return {
          ...prev,
          address: newAddress
        }
      })
    } else if (name === 'zipCode') {
      // Обработка прямого обновления индекса
      // console.log('Прямое обновление индекса:', value, e.target.isValid)

      // Обновляем индекс, но сохраняем locationData и streetData
      setOrderData((prev) => {
        return {
          ...prev,
          address: {
            ...prev.address,
            [name]: value
          }
        }
      })
    } else {
      // Обычное обновление других полей адреса
      setOrderData((prev) => ({
        ...prev,
        address: {
          ...prev.address,
          [name]: value
        }
      }))
    }
  }

  // Обработчик выбора организации
  const handleOrgSelect = (org: any) => {
    // console.log('Checkout handleOrgSelect:', org)
    setSelectedOrg(org)

    // Сохраняем текущие банковские реквизиты
    setOrderData((prev) => ({
      ...prev,
      recipient: {
        ...prev.recipient,
        companyName: org?.value || '',
        inn: org?.data?.inn || '',
        kpp: org?.data?.kpp || '',
        ogrn: org?.data?.ogrn || '',
        address: org?.data?.address?.unrestricted_value || org?.data?.address?.value || '',
        // Сохраняем банковские реквизиты
        bank: prev.recipient.bank,
        bik: prev.recipient.bik,
        rschet: prev.recipient.rschet,
        kschet: prev.recipient.kschet
      }
    }))
  }

  // Обработчик выбора банка
  const handleBankSelect = (bank: any) => {
    // console.log('Checkout handleBankSelect - before:', orderData.recipient)
    setSelectedBank(bank)

    setOrderData((prev) => ({
      ...prev,
      recipient: {
        ...prev.recipient,
        bank: bank?.value || '',
        bik: bank?.data?.bic || '',
        rschet: bank?.data?.account || '',
        kschet: bank?.data?.correspondent_account || '',
        // Сохраняем данные организации
        companyName: prev.recipient.companyName,
        inn: prev.recipient.inn,
        kpp: prev.recipient.kpp,
        ogrn: prev.recipient.ogrn
      }
    }))
  }

  // handleShippingMethodChange теперь единственный способ обновления shipping
  const handleShippingMethodChange = (method: ShippingMethodType) => {
    if (isValidZipCode) {
      setSelectedShippingMethod(method.name)
      setShippingPrice(method.price || 0)
      setOrderData((prev) => ({
        ...prev,
        shipping: {
          shortTitle: method.shortTitle,
          method: method.shortTitle, // теперь сохраняем человекочитаемое название
          price: method.price || 0,
          notice: method.note || ''
        }
      }))
    }
  }

  const handleNoticeChange = (value: string) => {
    setOrderData((prev) => ({
      ...prev,
      notice: value
    }))
  }

  // Маппинг для отображения названия и описания метода доставки
  const shippingMethodInfo: Record<string, { title: string; note?: string }> = {
    express: { title: 'Курьер', note: '' },
    standard: { title: 'Почта РФ', note: 'Клиент получает заказ самостоятельно в своем почтовом отделении.' },
    ems: { title: 'EMS', note: 'Отправка в течение 3-х рабочих дней...' },
    cdek: { title: 'СДЭК', note: 'Экспресс-доставка...' }
  }

  // --- Загрузка списка стран для AddressForm ---
  const [countries, setCountries] = useState<Country[]>([])
  const [countriesLoading, setCountriesLoading] = useState(true)
  useEffect(() => {
    fetch('/api/service/countries/ru')
      .then((r) => r.json())
      .then((data) => {
        setCountries(data)
        setCountriesLoading(false)
      })
      .catch(() => {
        setCountries([
          {
            country_id: 1,
            iso: '643',
            title: 'Россия'
          }
        ])
        setCountriesLoading(false)
      })
  }, [])

  // Настройка шагов оформления с использованием вынесенных форм
  const steps = [
    {
      title: 'Получатель',
      content: (
        <>
          <PersonalForm
            handleNext={handleNext}
            data={orderData.recipient}
            onChange={handleRecipientChange}
            onToggleLegalEntity={(value) => {
              setOrderData((prev) => ({
                ...prev,
                recipient: {
                  ...prev.recipient,
                  isLegalEntity: value
                }
              }))
            }}
            onOrgSelect={handleOrgSelect}
            onBankSelect={handleBankSelect}
            selectedOrg={selectedOrg}
            selectedBank={selectedBank}
            onValidityChange={(isValid) => handleValidityChange('recipient', isValid)}
          />
        </>
      )
    },
    {
      title: 'Адрес доставки',
      content: countriesLoading ? (
        <div className='py-8 text-center flex justify-center'><Spinner color='default'/></div>
      ) : (
        <AddressForm
          handleNext={handleNext}
          data={orderData.address}
          onChange={handleAddressChange}
          onValidityChange={(isValid) => handleValidityChange('address', isValid)}
          countries={countries}
        />
      )
    },
    {
      title: 'Доставка',
      content: (
        <ShippingMethods
          countryId={orderData.address.countryData?.iso}
          handleNext={handleNext}
          shippingParams={shippingParams}
          apiSettings={stableApiSettings}
          onSelect={handleShippingMethodChange}
          onValidityChange={(isValid) => handleValidityChange('shipping', isValid)}
          selectedShippingMethod={orderData.shipping.method}
        />
      )
    },
    {
      title: 'Форма оплаты',
      content: (
        <PaymentMethods
          handleNext={handleNext}
          locale='ru'
          apiSettings={{}}
          countryId={orderData.address.countryData?.iso}
          whosalePrices={$cartSums?.whosalePrices}
          selectedPayment={orderData.payment?.method || ''}
          onPayment={(method) => {
            if (!method) return
            setOrderData((prev) => ({
              ...prev,
              payment: {
                method: method.name,
                title: method.title, // Сохраняем title для отображения
                note: method.note
              }
            }))
          }}
          onValidityChange={(isValid) => handleValidityChange('payment', isValid)}
        />
      )
    },
    {
      title: 'Подтверждение',
      content: (
        <div>
          <div className='border-b pb-4'>
            <h2 className='text-2xl font-light tracking-tight'>Подтверждение заказа</h2>
          </div>

          {/* Order summary */}
          <div className='border-b py-6'>
            <div className='mb-4 flex justify-between'>
              <span className='text-muted-foreground text-sm'>Товары ({$cartTotal})</span>
              <span className='font-medium'>{priceFormat($cartSums.sum)}</span>
            </div>
            <div className='mb-4 flex justify-between'>
              <span className='text-muted-foreground text-sm'>Доставка</span>
              <span className='font-medium'>{priceFormat(orderData.shipping.price)}</span>
            </div>
            <div className='flex justify-between text-lg'>
              <span className='font-medium'>Итого</span>
              <span className='font-semibold'>{priceFormat($cartSums.sum + orderData.shipping.price)}</span>
            </div>
          </div>

          {/* Sections */}
          <div className='space-y-5 py-6'>
            {/* Recipient */}
            <div>
              <div className='mb-2 flex items-center justify-between'>
                <h3 className='text-muted-foreground text-sm font-medium uppercase tracking-wider'>Получатель</h3>
                {/* <Button variant="ghost" size="sm" className="h-6 px-2 text-xs">
                  Изменить <ChevronRightIcon className="ml-1 h-3 w-3" />
                </Button> */}
              </div>
              <div className='bg-muted/30 rounded p-3'>
                <p className='font-medium'>{orderData.recipient.fullName || `${orderData.recipient.fullName}`}</p>
                <p className='mt-1 text-sm'>{orderData.recipient.phone}</p>
                <p className='text-muted-foreground text-sm'>{orderData.recipient.email}</p>
              </div>
            </div>

            {/* Address */}
            <div>
              <div className='mb-2 flex items-center justify-between'>
                <h3 className='text-muted-foreground text-sm font-medium uppercase tracking-wider'>Адрес доставки</h3>
                {/* <Button variant="ghost" size="sm" className="h-6 px-2 text-xs">
                  Изменить <ChevronRightIcon className="ml-1 h-3 w-3" />
                </Button> */}
              </div>
              <div className='bg-muted/30 rounded p-3'>
                <p>
                  {orderData.address.country}, {orderData.address.location}
                </p>
                <p className='mt-1 text-sm'>
                  ул. {orderData.address.street}, д. {orderData.address.house}, кв. {orderData.address.apartment}
                </p>
                <p className='text-muted-foreground text-sm'>индекс: {orderData.address.zipCode}</p>
              </div>
            </div>

            {/* Shipping */}
            <div>
              <div className='mb-2 flex items-center justify-between'>
                <h3 className='text-muted-foreground text-sm font-medium uppercase tracking-wider'>Доставка</h3>
              </div>
              <div className='ml-3'>
                <div>{orderData.shipping.method}</div>
              </div>
            </div>

            {/* Payment */}
            <div>
              <div className='mb-2 flex items-center justify-between'>
                <h3 className='text-muted-foreground text-sm font-medium uppercase tracking-wider'>Оплата</h3>
                {/* <Button variant="ghost" size="sm" className="h-6 px-2 text-xs">
                  Изменить <ChevronRightIcon className="ml-1 h-3 w-3" />
                </Button> */}
              </div>
              <div className='ml-3'>
                <div className='flex items-center'>
                  <p>{orderData.payment.title}</p>
                </div>
              </div>
            </div>
            {/* Добавляем поле для примечания */}
            <div className='my-4'>
              <Textarea
                label='Примечание к заказу'
                labelPlacement='outside'
                rows={4}
                placeholder='Укажите дополнительную информацию к заказу, если необходимо'
                value={orderData.notice || ''}
                onValueChange={handleNoticeChange}
              />
            </div>
          </div>
        </div>
      )
    }
  ]

  useEffect(() => {
    if (currentStep === steps.length - 1) {
      // console.log('[Checkout] orderData.shipping на последнем шаге:', orderData.shipping)
    }
  }, [currentStep, orderData.shipping])

  // Логируем shipping.method при каждом рендере и переходе шага
  useEffect(() => {
    // console.log('[DEBUG] Текущий шаг:', currentStep, '| shipping.method:', orderData.shipping.method)
  }, [currentStep, orderData.shipping.method])

  return (
    <>
      <div className='cart-errors-block mb-4'>
        {filteredErrors.length > 0 && filteredWarnings.length > 0 && (
          <div className='mb-2 flex justify-end'>
            <Button size='sm' color='default' variant='flat' onPress={dismissAllNotifications} startContent={<XIcon className='h-4 w-4' />}>
              Скрыть все уведомления
            </Button>
          </div>
        )}
        <AnimatePresence>
          {filteredErrors.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
              className='mb-3'
            >
              <Card className='border-danger-200 bg-danger-50 shadow-sm'>
                <CardBody className='p-0'>
                  {filteredErrors.map((item) => (
                    <motion.div
                      key={item.sku}
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0, height: 0 }}
                      transition={{ duration: 0.2 }}
                      className='border-b border-danger-200 last:border-b-0'
                    >
                      <Alert color='danger' className='rounded-none border-0 py-3'>
                        <div className='flex items-center justify-between gap-2'>
                          <div className='flex items-center gap-2'>
                            <XCircleIcon className='h-5 w-5 text-danger-600' />
                            <div>
                              <span className='font-semibold'>{item.sku}:</span> Запрос: <span className='font-semibold'>{item.query}</span>, наличие:{' '}
                              <span className='font-semibold'>{item.stock}</span>.<span className='ml-1 font-medium'>Товар удален из корзины.</span>
                            </div>
                          </div>
                          <Button isIconOnly size='sm' variant='light' className='h-6 w-6 min-w-0 rounded-full' onPress={() => dismissError(item.sku)}>
                            <XIcon className='h-4 w-4' />
                          </Button>
                        </div>
                      </Alert>
                    </motion.div>
                  ))}
                </CardBody>
              </Card>
            </motion.div>
          )}

          {filteredWarnings.length > 0 && (
            <motion.div initial={{ opacity: 0, y: -10 }} animate={{ opacity: 1, y: 0 }} exit={{ opacity: 0, height: 0 }} transition={{ duration: 0.3 }}>
              <Card className='border-warning-200 bg-warning-50 shadow-sm'>
                <CardBody className='p-0'>
                  {filteredWarnings.map((item) => (
                    <motion.div
                      key={item.sku}
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0, height: 0 }}
                      transition={{ duration: 0.2 }}
                      className='border-b border-warning-200 last:border-b-0'
                    >
                      <Alert color='warning' className='rounded-none border-0 py-3'>
                        <div className='flex items-center justify-between gap-2'>
                          <div className='flex items-center gap-2'>
                            <AlertTriangleIcon className='h-5 w-5 text-warning-600' />
                            <div>
                              <span className='font-semibold'>{item.sku}:</span> Запрос: <span className='font-semibold'>{item.query}</span>, наличие:{' '}
                              <span className='font-semibold'>{item.stock}</span>.<span className='ml-1 font-medium'>Количество в корзине изменено.</span>
                            </div>
                          </div>
                          <Button isIconOnly size='sm' variant='light' className='h-6 w-6 min-w-0 rounded-full' onPress={() => dismissWarning(item.sku)}>
                            <XIcon className='h-4 w-4' />
                          </Button>
                        </div>
                      </Alert>
                    </motion.div>
                  ))}
                </CardBody>
              </Card>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
      <div>
        <CartSum
          key={shippingPrice}
          selectedShippingMethod={selectedShippingMethod}
          shippingType='standard'
          countryId={orderData.address?.countryData?.iso}
          destinationIndex={orderData.address.zipCode}
          shippingPrice={shippingPrice}
        />
      </div>

      {!$cartIsLoading && $cartTotal === 0 ? (
        <div className='relative mx-auto md:container'>
          <div className='flex flex-col items-center justify-center p-8 text-center'>
            <Alert color='warning' className='mb-4 max-w-2xl'>
              Ваша корзина пуста. Добавьте товары в корзину, чтобы оформить заказ.
            </Alert>
            <Button variant='bordered' as='a' href='/'>
              <HomeIcon /> На главную
            </Button>
          </div>
        </div>
      ) : (
        <div className='checkout-c relative'>
          <div className='flex flex-col justify-center gap-6 p-1 md:flex-row md:items-start'>
            {/* Боковая навигация по шагам */}
            <div className='z-20 hidden pb-8 sm:block md:sticky md:top-24 md:h-fit md:w-64 md:self-start'>
              <div className='flex gap-2 md:flex-col'>
                {steps.map((s, i) => (
                  <div key={i}>
                    <div className='flex items-center gap-2'>
                      <Button
                        isIconOnly
                        isDisabled
                        variant={i <= currentStep ? 'ghost' : 'flat'}
                        // onPress={() => setCurrentStep(i)}
                      >
                        <div className='text-sm'>{i < currentStep ? <CheckIcon className='w-5 text-success-600' strokeWidth={3} /> : i + 1}</div>
                      </Button>
                      <div className='hidden text-sm sm:block'>{s.title}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            {/* Контент выбранного шага */}
            <div className='rounded-lg' ref={contentRef}>
              {steps.map(
                (step, index) =>
                  index <= currentStep && (
                    <motion.div
                      key={index}
                      data-step={index}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3 }}
                      className='mb-8 rounded-lg border p-2 dark:border-default-300 dark:bg-default-50'
                    >
                      <h2 className='mb-4 text-xl font-semibold'>{step.title}</h2>
                      {step.content}
                      {validationErrors[steps[index].title] && (
                        <div className='mt-2 text-red-500'>
                          {Object.values(validationErrors[steps[index].title])
                            .flat()
                            .map((error, index) => (
                              <div key={index}>{error}</div>
                            ))}
                        </div>
                      )}
                    </motion.div>
                  )
              )}
              <div className='flex justify-end'>
                {/* <Button variant='outline' onPress={handlePrev} disabled={currentStep === 0}>
                Назад
              </Button> */}
                {currentStep === steps.length - 1 && (
                  <Button size='lg' isLoading={isLoading} color='warning' variant='flat' onPress={handleNext}>
                    {currentStep === steps.length - 1 ? (isLoading ? 'Отправка заказа' : 'Оформить заказ') : 'Далее'}
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  )
}
