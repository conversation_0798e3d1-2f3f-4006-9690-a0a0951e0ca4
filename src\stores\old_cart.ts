import { atom } from 'nanostores'

export type CartItem = {
  id: number
  qty: number
  price?: number
}

type CartType = {
  [id: string | number]: CartItem
}

type UpdCartItem = Pick<CartItem, 'id' | 'qty'>

let i = 0

//console.log('cart.ts imported: ', i++)


// export const cartItems = atom<CartType>(initCart())
export const cartItems = atom<CartType>({})
export const cartTotal = atom(0)

export const getCartTotal = () => {
  return cartTotal.get()
}
export const getTotalItems = () => {
  const cart = cartItems.get()
  return Object.values(cart).length
}

export function getQty(id: number | string) {
  return cartItems.get()[id]?.qty || 0
}
export function getCartItem(id: number | string): CartItem | undefined {
  return cartItems.get()[id]
}

export async function updateCartItem({ id, qty }: UpdCartItem) {
  //console.log("🚀 ~ updateCartItem ~ { id, qty }:", { id, qty })
  if (qty < 1) {
    const v = cartItems.get()
    delete v[id]
    cartItems.set({
      ...v
    })
  } else {
    const existingEntry = cartItems.get()[id]
    if (existingEntry) {
      cartItems.set({
        ...cartItems.get(),
        [id]: {
          ...existingEntry,
          qty
        }
      })
    } else {
      cartItems.set({
        ...cartItems.get(),
        [id]: {
          id,
          qty
        }
      })
    }
  }
}

cartItems.subscribe((val) => {
  //console.log('@change cartItems: ', val);
})

export function clearCart() {
  cartItems.set({})
}
