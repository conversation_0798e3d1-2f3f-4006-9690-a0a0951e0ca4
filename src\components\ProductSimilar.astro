---
import { trpc } from '@/trpc'
import { ProductList } from './react/ProductList'

import ProductsListMinify from '@/components/ProductsListMinify.astro'

interface Props {
  searchvalue: string
  isMobile?: boolean
}

const { searchvalue, isMobile = true } = Astro.props

const response = await trpc.products.globalSearch.query({
  limit: 10,
  page: 1,
  searchvalue
})

---

<div class='mt-5 gap-5'>
  {
    response.categories.map((categoryData) => (
      <div class='mb-5 rounded-lg border p-2'>
        {/* {response.categories?.length > 1 && <h2 class='text-base pb-2'>Найдено в {String(categoryData.categoryTitle || 'каталоге').toLowerCase()}</h2>} */}
        {/* {<h2 class='pb-2 text-base'>{categoryData.categoryTitle || ''}</h2>} */}

        <ProductList
          data={{
            columns: categoryData.columns,
            data: categoryData.products
          }}
          client:visible
          enableSorting={false}
          viewMode={isMobile ? 'grid' : 'table'}
        />
        {/* <div class="empty-placeholder max-h-10"></div> */}
      </div>
    ))
  }
</div>
<!-- <ProductsListMinify tableData={response} /> -->
