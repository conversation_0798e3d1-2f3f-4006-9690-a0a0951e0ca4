---
import { BackHistoryButton } from '@components/react/BackHistoryButton'
import { Alert } from '@heroui/react'
import Layout from '@layouts/Layout.astro'

Astro.response.status = 404
Astro.response.headers.set('status', '404')
Astro.response.statusText = 'Not Found'
---

<Layout title='Товар не найден' description=''>
  <div class='-mt-5 flex h-96 flex-col items-center justify-center gap-5 bg-danger-100/20 p-2'>
    <div class='mt-5 sm:mt-10'><Alert color='danger' className='text-lg font-bold'>Товар не найден или удален.</Alert></div>
    <div><BackHistoryButton label='На главную' goto='/' client:idle /></div>
  </div>
</Layout>
