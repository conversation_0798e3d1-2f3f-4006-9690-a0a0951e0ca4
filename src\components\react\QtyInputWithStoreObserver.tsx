import { useStore } from '@tanstack/react-store'
import { cartItems } from '@/stores/cart'
import { QtyInput } from '@components/react/QtyInput'
import type { CategoryProduct } from '@/types/GetCategoryProduct'
import { memo, useEffect, useState } from 'react'
import { QtyInputSkeleton } from './QtyInputSkeleton'

interface Props {
  product: CategoryProduct
  label?: string
  [key: string]: any
}

export const QtyInputWithStoreObserver = memo(({ product, label = 'В корзину', ...rest }: Props) => {
  const $cartItems = useStore(cartItems)
  const qty = $cartItems[product.prod_id]?.qty || 0

  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  return !mounted ? <QtyInputSkeleton qty={qty} /> : <QtyInput key={qty} product={product} initialValue={qty} label={label} {...rest} />
})
