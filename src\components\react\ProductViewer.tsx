import { memo, useState, useCallback } from 'react'
import { useStore } from '@nanostores/react'
import { currentPageStore, updateURLParams } from '@/stores/qs'
import { ProductList } from './ProductList'
import { Button } from '@heroui/react'
import { trpc } from '@/trpc'

const List = memo(({ data, ...props }) => <ProductList data={data} {...props} />)

export const ProductViewer = ({ data, editorModeEnable = false, ...props }) => {
  const [items, setItems] = useState(data)
  const [isLoading, setIsLoading] = useState(false)
  const currentPage = useStore(currentPageStore)

  const loadMore = useCallback(async () => {
    if (isLoading) return
    setIsLoading(true)

    // Сохраняем текущую позицию прокрутки
    const scrollPosition = window.scrollY || window.pageYOffset

    try {
      const nextPage = currentPage + 1
      // const newData = await trpc.products.getCategoryProduct.query({
      const newData = await trpc.products.getCategoryProductMeili.query({
        identifier: props.categoryId,
        limit: props.limit,
        page: nextPage,
        order: props.order,
        filters: props.filters
      })

      setItems((prev: any) => ({
        ...prev,
        products: {
          ...prev.products,
          data: [...prev.products.data, ...newData.products.data]
        }
      }))

      updateURLParams({ page: nextPage }, undefined, true)
      currentPageStore.set(nextPage)

      // Восстанавливаем позицию прокрутки после обновления URL
      setTimeout(() => {
        window.scrollTo({
          top: scrollPosition,
          behavior: 'auto'
        })
      }, 0)
    } finally {
      setIsLoading(false)
    }
  }, [currentPage, props.categoryId, props.limit, props.order, props.filters])

  const totalPages = Math.ceil((items.products?.meta?.totalCount || 0) / props.limit)
  const hasMore = currentPage < totalPages

  return (
    <div>
      <List data={items} editorModeEnable={editorModeEnable} {...props} />

      {hasMore && (
        <div className='mt-4 flex flex-col items-center gap-2'>
          {/* <Chip
            color="primary"
            variant="flat"
            className="text-sm"
          >
            Страница {currentPage} из {totalPages}
          </Chip> */}
          <Button onPress={loadMore} isLoading={isLoading}>
            {isLoading ? 'Загрузка...' : 'Показать еще'}
          </Button>
        </div>
      )}
    </div>
  )
}
