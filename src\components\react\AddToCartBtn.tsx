import { useState } from 'react'
import { Button, Input } from '@heroui/react'
import { ShoppingCart, Plus, Minus } from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'

export default function AddToCartButton() {
  const [isAdded, setIsAdded] = useState(false)
  const [quantity, setQuantity] = useState(1)

  const handleAdd = () => {
    setIsAdded(true)
  }

  const handleIncrement = () => {
    setQuantity((prev) => prev + 1)
  }

  const handleDecrement = () => {
    setQuantity((prev) => (prev > 1 ? prev - 1 : 1))
  }

  return (
    <div className='flex items-center justify-center p-4'>
      <AnimatePresence mode='wait'>
        {!isAdded ? (
          <motion.div
            key='button'
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            transition={{ duration: 0.3 }}
          >
            <Button color='warning' variant='solid' startContent={<ShoppingCart size={20} />} onPress={handleAdd}>
              Add to Cart
            </Button>
          </motion.div>
        ) : (
          <motion.div
            key='counter'
            className='flex items-center space-x-2'
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            transition={{ duration: 0.3 }}
          >
            <Button isIconOnly color='primary' variant='flat' onPress={handleDecrement}>
              <Minus size={20} />
            </Button>
            <Input type='number' value={quantity.toString()} onChange={(e) => setQuantity(parseInt(e.target.value) || 1)} className='w-16 text-center' />
            <Button isIconOnly color='primary' variant='flat' onPress={handleIncrement}>
              <Plus size={20} />
            </Button>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
