import { Button } from '@heroui/react'
import { MoonIcon, SunIcon } from 'lucide-react'
import { useEffect, useState } from 'react'

export const ThemeToggle = ({ size = 'md', "aria-label": ariaLabel = 'Переключить тему' }) => {
  const [theme, setTheme] = useState<'theme-light' | 'dark' | 'system'>('theme-light')

  useEffect(() => {
    const isDarkMode = document.documentElement.classList.contains('dark')
    setTheme(isDarkMode ? 'dark' : 'theme-light')
  }, [])

  useEffect(() => {
    const isDark = theme === 'dark' || (theme === 'system' && window.matchMedia('(prefers-color-scheme: dark)').matches)
    document.documentElement.classList[isDark ? 'add' : 'remove']('dark')

    localStorage.theme = theme
  }, [theme])

  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) return null

  return (
    <Button
      isIconOnly
      color='default'
      size={size}
      onPress={() => setTheme(theme === 'dark' ? 'theme-light' : 'dark')}
      aria-label={ariaLabel}
      aria-pressed={theme === 'dark'}
    >
      {theme === 'dark' ? <SunIcon aria-hidden="true" /> : <MoonIcon aria-hidden="true" />}
    </Button>
  )
}
