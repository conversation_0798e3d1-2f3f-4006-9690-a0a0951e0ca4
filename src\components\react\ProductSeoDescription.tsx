import { appSettingsStore, getSettings } from '@/stores/settings'
import { useStore } from '@tanstack/react-store'

function stringToTemplateLiteral(str, data) {
  return str.replace(/\${(\w+)\}/g, (match, varName) => {
    return data[varName] !== undefined ? data[varName] : ''
  })
}

function getSeoString({ seo_templates, product }) {
  try {
    // console.log("🚀 ~ getSeoString ~ $apiSettings['seo_templates']:", $apiSettings['seo_templates'])
    const str = seo_templates?.find((x) => x?.cat_id == Number(product.prod_cat)).seo_template
    return stringToTemplateLiteral(str, product)
  } catch (error) {
    return ''
  }
}
export const ProductSeoDescription = ({ product }) => {
  const $appSettingsStore = useStore(appSettingsStore)

  return <h3 className='line-height-1.5 my-5 p-3 text-sm text-default-500'>{getSeoString({ product, seo_templates: $appSettingsStore?.seo_templates })}</h3>
}
