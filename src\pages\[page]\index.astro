---
import { trpc } from '@/trpc'
import Layout from '@layouts/Layout.astro'

const pageID = Astro.params.page
const page = await trpc.services.getPage.query({ id: pageID })

// Устанавливаем заголовки кеширования
Astro.response.headers.set('Cache-Control', 'public, max-age=7200, stale-while-revalidate=7200')
Astro.response.headers.set('Vary', 'Accept-Encoding');
---

<Layout title={page?.page_title || '404'}>
  <h1 class="text-center text-lg font-semibold my-5">{page?.page_title}</h1>
  <div class='text-balance p-3 py-5 my-5 text-default-900 md:p-5'>
    <div set:html={page?.page_body} />
  </div>
</Layout>
