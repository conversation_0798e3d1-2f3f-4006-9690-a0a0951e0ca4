import { memo } from 'react'
import { Button } from '@heroui/react'
import { Edit3Icon } from 'lucide-react'
import { openchunkEditor } from '@/stores/htmlEditor'

interface HtmlEditorButtonProps {
    chunkId?: number
    chunkKey?: string
    size?: 'sm' | 'md' | 'lg'
    label?: string
}

export const HtmlEditorButton = memo(({ chunkId, chunkKey, size = 'md', label = 'Редактировать' }: HtmlEditorButtonProps) => {
    return (
        <Button
            color="default"
            variant="bordered"
            size={size}
            isIconOnly={!label || label === ' '}
            startContent={<Edit3Icon className="h-4 w-4" />}
            onPress={() => openchunkEditor({ chunkId: chunkId, chunkKey: chunkKey })}
        >
            {label}
        </Button>
    )
})