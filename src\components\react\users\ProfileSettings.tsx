import { PersonalForm } from './PersonalForm'
import { AddressForm } from './AddressForm'
import { useState, useEffect } from 'react'
import { addToast, Skeleton, Spinner } from '@heroui/react'
import type { Country } from '@/types/form'

// Интерфейс данных, которые приходят с сервера
interface ClientData {
  client_id: number
  client_number: number
  client_name: string
  client_mail: string
  client_phone: string
  client_country: string
  client_city: string
  client_street: string
  client_house: string
  client_flat: string
  client_postindex: string
  client_discount: number
  client_cdekid: string
  remember_me_token: any
  created_at: string
  updated_at: string
}

// Интерфейс для PersonalForm
interface UserFormData {
  fullName: string
  email: string
  phone: string
  isLegalEntity: boolean
  companyName: string
  inn: string
  kpp: string
  ogrn: string
  address: string
  bank: string
  bik: string
  rschet: string // добавляем расчетный счет
  kschet: string // добавляем корреспондентский счет
}

// Добавляем интерфейс для адресных данных формы
interface AddressFormData {
  country: string
  countryData: any
  location: string
  locationData: any
  street: string
  house: string
  apartment: string
  zipCode: string
}

export const ProfileSettings = () => {
  const [clientData, setClientData] = useState<ClientData | null>(null)

  // Состояние для PersonalForm остается как есть
  const [userData, setUserData] = useState<UserFormData>({
    fullName: '',
    email: '',
    phone: '',
    isLegalEntity: false,
    companyName: '',
    inn: '',
    kpp: '',
    ogrn: '',
    address: '',
    bank: '',
    bik: '',
    rschet: '', // инициализируем расчетный счет
    kschet: '' // инициализируем корреспондентский счет
  })

  // Обновляем структуру состояния адреса в соответствии с Checkout
  const [formData, setFormData] = useState({
    address: {
      country: 'Россия',
      // countryData: {
      //   country_id: 1,
      //   iso: '643',
      //   title: 'Россия'
      // },
      location: '',
      locationData: null,
      street: '',
      house: '',
      apartment: '',
      zipCode: ''
    }
  })

  // --- Список стран для AddressForm ---
  const [countries, setCountries] = useState<Country[]>([])
  const [countriesLoading, setCountriesLoading] = useState(true)
  useEffect(() => {
    fetch('/api/service/countries/ru')
      .then((r) => r.json())
      .then((data) => {
        setCountries(data)
        setCountriesLoading(false)
      })
      .catch(() => {
        setCountries([])
        setCountriesLoading(false)
      })
  }, [])

  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const response = await fetch('/api/auth/login/check', {
          credentials: 'include'
        })
        if (!response.ok) {
          addToast({
            title: 'Ошибка!',
            description: 'Ошибка авторизации, попробуйте позже',
            variant: 'flat',
            color: 'danger'
          })
          throw new Error('Ошибка загрузки данных')
        }
        const data: ClientData = await response.json()

        setClientData(data)

        // Обновляем установку данных для PersonalForm
        setUserData({
          fullName: data.client_name || '',
          email: data.client_mail || '',
          phone: data.client_phone || '',
          isLegalEntity: !!data.org,
          companyName: data.org?.org_name || '',
          inn: data.org?.org_inn || '',
          kpp: data.org?.org_kpp || '',
          ogrn: data.org?.org_name || '',
          address: data.org?.org_name || '',
          bank: data.org?.org_bank || '',
          bik: data.org?.org_bik || '',
          rschet: data.org?.org_rschet || '', // устанавливаем расчетный счет из данных
          kschet: data.org?.org_kschet || '' // устанавливаем корреспондентский счет из данных
        })

        // Обновляем данные адреса с правильным маппингом
        const addressData = {
          country: data.client_country || 'Россия',
          // countryData: {
          //   country_id: 1,
          //   iso: '643',
          //   title: 'Россия'
          // },
          location: data.client_city || '',
          // locationData: data.client_city
          //   ? {
          //       location: data.client_city,
          //       index: data.client_postindex,
          //       city_fias_id: Date.now().toString(),
          //       settlement_fias_id: null,
          //       street_with_type: data.client_street,
          //     uniqueId: `${data.client_city}-${Date.now()}`
          //     }
          //   : null,
          street: data.client_street || '',
          house: data.client_house || '',
          apartment: data.client_flat || '',
          zipCode: data.client_postindex || ''
        }

        // Устанавливаем все поля сразу
        setFormData({
          address: addressData
        })
      } catch (error) {
        // console.error('Ошибка при загрузке профиля:', error)
      }
    }

    fetchUserData()
  }, [])

  // Обновленный handleChange - только обновляет локальное состояние без запросов на сервер
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target

    // Обновляем локальное состояние формы
    if (name === 'orgData' || name === 'bankData') {
      setUserData((prev) => ({
        ...prev,
        ...value
      }))
    } else {
      setUserData((prev) => ({
        ...prev,
        [name]: value
      }))
    }
  }

  const handleToggleLegalEntity = (value: boolean) => {
    setUserData((prev) => ({
      ...prev,
      isLegalEntity: value
    }))
  }

  // Обновленный handleAddressChange - поддержка addressPatch для атомарного обновления
  const handleAddressChange = (e: { target: { name: string; value: any; isValid?: boolean } }) => {
    const { name, value } = e.target

    // Если value — объект с полями title и countryData, обновляем оба поля
    if (name === 'country' && value && typeof value === 'object' && value.countryData) {
      setFormData((prev) => ({
        ...prev,
        address: {
          ...prev.address,
          country: value.title,
          countryData: value.countryData
        }
      }))
    } else if (name === 'addressPatch' && value && typeof value === 'object') {
      // Обновляем адрес
      setFormData((prev) => {
        // Создаем новый объект адреса с сохранением существующих данных
        const newAddress = { ...prev.address }

        // Сохраняем текущий индекс, чтобы проверить, изменился ли он
        const currentZipCode = newAddress.zipCode || ''

        // Сохраняем индекс из locationData, если он есть
        const locationIndex = newAddress.locationData?.index || ''

        // Обновляем только те поля, которые пришли в value
        Object.keys(value).forEach((key) => {
          // Если это locationData и он не null, сохраняем его
          if (key === 'locationData' && value[key]) {
            newAddress[key] = value[key]

            // Если в новом locationData есть индекс и текущий индекс пустой, используем его
            if (value[key].index && !currentZipCode) {
              newAddress.zipCode = value[key].index
            }
          }
          // Если это streetData и он не null, сохраняем его
          else if (key === 'streetData' && value[key]) {
            newAddress[key] = value[key]

            // Если в streetData есть индекс, используем его
            if (value[key].index) {
              newAddress.zipCode = value[key].index
            }
            // Если в streetData нет индекса, но есть в locationData, используем его
            else if (locationIndex && value.zipCode === '') {
              newAddress.zipCode = locationIndex
            }
          }
          // Если это zipCode и он пустой, но есть индекс в locationData, используем его
          else if (key === 'zipCode' && !value[key] && locationIndex) {
            newAddress[key] = locationIndex
          }
          // Для остальных полей просто обновляем значение
          else {
            newAddress[key] = value[key]
          }
        })

        return {
          ...prev,
          address: newAddress
        }
      })
    } else if (name === 'zipCode') {
      // Обработка прямого обновления индекса
      setFormData((prev) => ({
        ...prev,
        address: {
          ...prev.address,
          [name]: value
        }
      }))
    } else {
      // Обычное обновление других полей адреса
      setFormData((prev) => ({
        ...prev,
        address: {
          ...prev.address,
          [name]: value
        }
      }))
    }
  }

  // Обновляем handleSavePersonalData для соответствия структуре из Checkout
  const handleSavePersonalData = async () => {
    if (!clientData) return

    try {
      // Формируем основные данные клиента
      const updatedClientData = {
        ...clientData,
        client_name: userData.fullName,
        client_mail: userData.email,
        client_phone: userData.phone
      }

      // Формируем данные для организации в том же формате, что и в Checkout
      const requestBody = {
        client: updatedClientData
        // Добавляем org только если это юр.лицо
        // ...(userData.isLegalEntity && {
        //   org: {
        //     org_name: userData.companyName,
        //     org_adress: userData.address,
        //     org_inn: userData.inn,
        //     org_kpp: userData.kpp,
        //     org_rschet: userData.rschet || '',
        //     org_kschet: userData.kschet || '',
        //     org_bik: userData.bik || '',
        //     org_bank: userData.bank || '',
        //   }
        // })
      }

      if (userData.isLegalEntity) {
        requestBody.client.org = {
          org_name: userData.companyName,
          org_adress: userData.address,
          org_inn: userData.inn,
          org_kpp: userData.kpp,
          org_rschet: userData.rschet || '',
          org_kschet: userData.kschet || '',
          org_bik: userData.bik || '',
          org_bank: userData.bank || ''
        }
      }

      const response = await fetch('/api/client/update', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      })

      if (response.ok) {
        // console.log('Данные успешно обновлены')
        addToast({
          title: 'Успешно',
          description: 'Данные успешно обновлены',
          variant: 'bordered',
          color: 'success'
        })

        setClientData(updatedClientData)
      } else {
        addToast({
          title: 'Ошибка!',
          description: 'Ошибка при обновлении данных, попробуйте позже',
          variant: 'bordered',
          color: 'danger'
        })
      }
    } catch (error) {
      // console.error('Ошибка при обновлении профиля:', error)
    }
  }

  // Новая функция для сохранения адресных данных
  const handleSaveAddressData = async () => {
    // console.log('🚀 ~ handleSaveAddressData ~ clientData:', clientData)

    if (!clientData) return

    try {
      // Обновляем маппинг полей для API
      const updatedClientData = {
        ...clientData,
        client_country: formData.address.country, // <--- добавлено!
        client_city: formData.address.location,
        client_street: formData.address.street,
        client_house: formData.address.house,
        client_flat: formData.address.apartment,
        client_postindex: formData.address.zipCode
      }

      // Отправляем обновленные данные на сервер
      const response = await fetch('/api/client/update', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ client: updatedClientData })
      })

      if (response.ok) {
        addToast({
          title: 'Успешно',
          description: 'Адрес доставки успешно обновлен',
          variant: 'flat',
          color: 'success'
        })

        // Обновляем локальное состояние оригинальных данных
        setClientData(updatedClientData)
      } else {
        addToast({
          title: 'Ошибка!',
          description: 'Ошибка при обновлении данных, попробуйте позже',
          variant: 'bordered',
          color: 'danger'
        })
      }
    } catch (error) {
      // console.error('Ошибка при обновлении адреса:', error)
    }
  }

  return (
    <div className='mx-auto max-w-3xl p-4'>
      <h2 className='mb-6 text-2xl font-semibold'>Настройки профиля</h2>
      {clientData?.client_id && (
        <div className='space-y-8'>
          <div className='rounded border p-2 dark:border-default-200 md:p-3 xl:p-5'>
            <h3 className='mb-4 text-xl font-medium'>Личные данные</h3>
            <PersonalForm
              showNextPermanent
              nextBtnTitle='Сохранить'
              data={userData}
              onChange={handleChange}
              onToggleLegalEntity={handleToggleLegalEntity}
              handleNext={handleSavePersonalData}
              onValidityChange={() => {}}
            />
          </div>

          <div className='rounded border p-2 dark:border-default-200 md:p-3 xl:p-5'>
            <h3 className='mb-4 text-xl font-medium'>Адрес</h3>
            {countriesLoading ? (
              <div className='flex justify-center py-8 text-center'>
                <Spinner color='default' />
              </div>
            ) : (
              <AddressForm
                showNextPermanent
                iplocate={false}
                nextBtnTitle='Сохранить'
                data={formData.address}
                onChange={handleAddressChange}
                handleNext={handleSaveAddressData}
                onValidityChange={() => {}}
                countries={countries}
              />
            )}
          </div>
        </div>
      )}

      {!clientData?.client_id && (
        <div slot='fallback'>
          <div className='mx-auto max-w-3xl p-4'>
            <div className='mb-6'>
              <Skeleton className='h-8 w-40 rounded-lg' />
            </div>

            <div className='space-y-8'>
              {/* Скелетон для PersonalForm */}
              <div className='rounded border p-2 dark:border-default-200 md:p-3 xl:p-5'>
                <Skeleton className='mb-4 h-6 w-32 rounded-lg' />
                <div className='space-y-4'>
                  <Skeleton className='h-12 w-full rounded-lg' />
                  <Skeleton className='h-12 w-full rounded-lg' />
                  <Skeleton className='h-12 w-full rounded-lg' />
                </div>
                <div className='mt-4 flex w-full justify-end'>
                  <Skeleton disableAnimation className='h-10 w-28 rounded-lg bg-warning-100' />
                </div>
              </div>

              {/* Скелетон для AddressForm */}
              <div className='rounded border p-2 dark:border-default-200 md:p-3 xl:p-5'>
                <Skeleton className='mb-4 h-6 w-24 rounded-lg' />
                <div className='space-y-4'>
                  <div className='flex flex-wrap gap-4'>
                    <Skeleton className='h-12 w-52 rounded-lg' />
                    {/* Страна */}
                    <Skeleton className='h-12 flex-1' />
                    {/* Город */}
                  </div>
                  <Skeleton className='h-12 w-80' />
                  {/* Улица */}
                  <div className='grid grid-cols-2 gap-4'>
                    <Skeleton className='h-12 rounded-lg' />
                    {/* Дом */}
                    <Skeleton className='h-12 rounded-lg' />
                    {/* Квартира */}
                  </div>
                  <Skeleton className='h-12 w-40 rounded-lg' />
                  {/* Индекс */}
                  <div className='mt-4 flex w-full justify-end'>
                    <Skeleton disableAnimation className='h-10 w-28 rounded-lg bg-warning-100' />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
