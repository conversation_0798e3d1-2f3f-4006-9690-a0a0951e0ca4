import { atom } from 'nanostores'

export interface Client {
  client_id?: number
  client_number?: number
  client_name?: string
  client_mail?: string
  client_phone?: string
  client_country?: string
  client_adress?: string
  client_city?: string
  client_street?: string
  client_house?: string
  client_flat?: string
  client_postindex?: string
  client_discount?: number
  client_cdekid?: string
  remember_me_token?: any
  created_at?: string
  updated_at?: string
  isAuthenticated?: boolean
}

export const userStore = atom<Client>({})

export const setUser = (userData: Partial<Client>) => {
  userStore.set({ ...userStore.get(), ...userData })
}

export const clearUser = () => {
  userStore.set({  })
}