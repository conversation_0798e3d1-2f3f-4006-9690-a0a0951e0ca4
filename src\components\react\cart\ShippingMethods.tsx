import React, { useEffect, useState, useMemo, useCallback, useRef } from 'react'
import { RadioGroup, Radio, cn, Alert, Form, Button } from '@heroui/react'
import { useStore } from '@tanstack/react-store'
import { cartItems, cartSums } from '@/stores/cart'
import { priceFormat } from '@/lib/priceFormat'
import { TrpcReactWrapper } from '../TrcpReactWrapper'
import { trpcReact } from '@/trpc'
import { useForm, Controller } from 'react-hook-form'
import { useFreeShipping } from '@/lib/freeShipping'

import type { ShippingMethodType } from '@/types'

interface ShippingMethodsProps {
  countryId?: number
  locale?: string
  apiSettings: Record<string, any>
  onSelect: (method: ShippingMethodType) => void
  onShippingChange: (price: number, isLoading: boolean) => void
  handleNext: () => void
  onValidityChange?: (isValid: boolean) => void
}

const emsIcon = `<svg class="w-40 h-14" fill="none" viewBox="0 0 349 138">...</svg>`
const pochtaIcon = `<svg class="w-20 h-14" fill-rule="evenodd">...</svg>`

const CustomRadio = ({ children, ...props }: any) => (
  <Radio
    {...props}
    classNames={{
      base: cn(
        'm-0 bg-white dark:bg-default-50 hover:bg-default-100',
        'cursor-pointer rounded-lg gap-2 p-2 lg:p-4 border-2 dark:border-default-200',
        'data-[selected=true]:border-warning-500 appearance-none'
      )
    }}
  >
    {children}
  </Radio>
)

interface ShippingMethodsCalculatorProps extends ShippingMethodsProps {
  shippingParams: {
    destinationIndex?: string
    isValidZipCode?: boolean
  }
}

interface ShippingResult {
  type: string
  price: number
  error?: string
}

const ShippingMethodsCalculator = ({
  countryId = 643,
  locale = 'ru',
  apiSettings = {},
  onSelect,
  shippingParams,
  onShippingChange,
  handleNext,
  onValidityChange
}: ShippingMethodsCalculatorProps) => {
  const [showNextBtn, setShowNextBtn] = useState(true)
  const $cartSums = useStore(cartSums)
  const { destinationIndex } = shippingParams || {}
  const { isEligible, config } = useFreeShipping()

  // console.log("🚀 ~ shippingParams:", shippingParams)

  // Получаем флаг валидности индекса из параметров
  const isValidZipCode = shippingParams.isValidZipCode ?? false

  const { control, handleSubmit, watch, setValue, formState } = useForm({
    defaultValues: {
      shippingMethod: ''
    },
    mode: 'onChange'
  })

  const handleShippingChange = useCallback(
    (price: number, isLoading: boolean) => {
      if (!isLoading && price !== undefined && shippingParams.destinationIndex && isValidZipCode) {
        // console.log('ShippingMethods: handleShippingChange вызван с ценой:', price, 'индекс:', shippingParams.destinationIndex)
        onShippingChange?.(price, isLoading)
      } else {
        // console.log('ShippingMethods: handleShippingChange НЕ вызван, условия не выполнены:', {
        //   isLoading,
        //   price,
        //   destinationIndex: shippingParams.destinationIndex,
        //   isValidZipCode
        // })
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [shippingParams.destinationIndex, isValidZipCode]
  )

  const handleMethodSelect = useCallback(
    (method: ShippingMethodType) => {
      if (shippingParams.destinationIndex && isValidZipCode) {
        // console.log('ShippingMethods: handleMethodSelect вызван с методом:', method.name, 'индекс:', shippingParams.destinationIndex)
        onSelect?.(method)
        handleShippingChange(method.price || 0, false)
      } else {
        // console.log('ShippingMethods: handleMethodSelect НЕ вызван, индекс отсутствует или невалиден:', {
        //   destinationIndex: shippingParams.destinationIndex,
        //   isValidZipCode
        // })
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [handleShippingChange, shippingParams.destinationIndex, isValidZipCode]
  )

  // Отслеживаем изменение индекса, но НЕ сбрасываем выбранный метод доставки
  const prevIndexRef = useRef(shippingParams.destinationIndex)

  useEffect(() => {
    // Проверяем, действительно ли изменился индекс
    if (prevIndexRef.current !== shippingParams.destinationIndex) {
      // console.log('ShippingMethods: Индекс изменился:', {
      //   prev: prevIndexRef.current,
      //   current: shippingParams.destinationIndex
      // })

      // Обновляем сохраненное значение
      prevIndexRef.current = shippingParams.destinationIndex

      // Если есть выбранный метод доставки, пересчитываем его цену
      const selectedMethod = watch('shippingMethod')
      if (selectedMethod && shippingParams.destinationIndex && isValidZipCode) {
        // console.log('ShippingMethods: Пересчитываем цену для выбранного метода:', selectedMethod)
        // Запрос на пересчет доставки будет выполнен автоматически благодаря изменению shippingParams
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [shippingParams.destinationIndex])

  // Отслеживаем валидность формы
  useEffect(() => {
    onValidityChange?.(formState.isValid)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [formState.isValid])

  // Гарантируем актуальную валидность shipping при любом выборе/перерисовке
  useEffect(() => {
    onValidityChange?.(!!watch('shippingMethod'))
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [watch('shippingMethod')])

  const availableMethods = useMemo(() => {
    const baseMethods = [
      {
        title: 'Доставка курьерской службой',
        name: 'express',
        shortTitle: 'Курьер',
        active: true,
        note: '',
        price: 0,
        icon: '<svg>...</svg>'
      },
      {
        title: 'Доставка 1 классом почты России',
        name: 'standard',
        active: true,
        maxPrice: apiSettings?.['pochta.standard.maxValue'] || 20000,
        shortTitle: 'Почта РФ',
        note: 'Клиент получает заказ самостоятельно в своем почтовом отделении.',
        price: 0,
        icon: pochtaIcon
      },
      {
        title: 'Доставка курьерской службой EMS',
        name: 'ems',
        shortTitle: 'EMS',
        active: !!apiSettings?.['shipping.ems.active'],
        note: 'Отправка в течение 3-х рабочих дней...',
        price: 0,
        icon: emsIcon
      },
      {
        title: 'Доставка курьерской службой СДЭК',
        name: 'cdek',
        shortTitle: 'СДЭК',
        active: !!apiSettings?.['shipping.cdek.active'] && $cartSums?.sum > 15000,
        note: 'Экспресс-доставка...',
        price: 0,
        icon: '<svg>...</svg>'
      }
    ]

    const euMethods = [
      {
        title: 'Standard',
        name: 'standard',
        shortTitle: 'Standard',
        maxPrice: 999999999,
        active: countryId !== 616,
        note: '',
        price: 0,
        icon: '<svg>...</svg>'
      }
    ]

    return locale !== 'ru' ? euMethods.filter((m) => m.active) : baseMethods.filter((m) => m.active && (!m.maxPrice || $cartSums?.sum <= m.maxPrice))
  }, [locale, countryId, $cartSums?.sum, apiSettings])

  // Запрос на расчет стоимости доставки - включаем только если индекс валидный
  const shippingQuery = trpcReact.services.calculateShipping.useQuery(
    {
      destinationIndex: Number(destinationIndex),
      country: Number(countryId),
      type: availableMethods.map((m) => m.name)
    },
    {
      // Запрос включается только если индекс валидный и есть доступные методы
      enabled: !!(destinationIndex && isValidZipCode && availableMethods.length > 0),
      refetchOnMount: true, // Включаем пересчет при монтировании компонента
      refetchOnWindowFocus: false,
      retry: false
    }
  )

  const methodsWithPrices = useMemo((): ShippingMethodType[] => {
    if (!shippingQuery.data) return availableMethods

    return availableMethods.map((method) => {
      const shippingResult = shippingQuery.data.find((r: ShippingResult) => r.type === method.name)
      const originalPrice = shippingResult?.price || 0
      const finalPrice = isEligible ? 0 : originalPrice

      return {
        ...method,
        price: finalPrice,
        originalPrice,
        error: shippingResult?.error,
        isFree: isEligible
      }
    })
  }, [availableMethods, shippingQuery.data, isEligible])

  // Обновляем shipping в Checkout при изменении цены для выбранного метода
  useEffect(() => {
    if (!shippingQuery.isLoading && shippingQuery.data) {
      const selectedMethodName = watch('shippingMethod')
      if (selectedMethodName) {
        const method = methodsWithPrices.find((m) => m.name === selectedMethodName)
        if (method && method.price !== undefined) {
          // Передаем наверх актуальный объект метода (с новой ценой)
          onSelect?.(method)
        }
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [shippingQuery.data, shippingQuery.isLoading])

  // Добавляем эффект для пересчета доставки при изменении корзины
  useEffect(() => {
    // Если сумма корзины изменилась и есть выбранный метод доставки, запускаем пересчет
    if (shippingParams.destinationIndex && isValidZipCode) {
      // Принудительно запускаем пересчет доставки
      shippingQuery.refetch()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [$cartSums?.sum])

  const onSubmit = () => {
    handleNext()
    setShowNextBtn(false)
  }

  return (
    <>
      <Form onSubmit={handleSubmit(onSubmit)}>
        <Controller
          control={control}
          name='shippingMethod'
          rules={{ required: 'Выберите способ доставки' }}
          render={({ field: { ref, value, onChange }, fieldState: { invalid, error } }) => (
            <RadioGroup
              ref={ref}
              isRequired
              isDisabled={shippingQuery.isFetching || shippingQuery.isRefetching}
              orientation='horizontal'
              label='Способ доставки'
              color='warning'
              value={value}
              onValueChange={(value) => {
                // Проверяем наличие и валидность индекса
                if (shippingParams.destinationIndex && isValidZipCode) {
                  onChange(value)
                  const method = methodsWithPrices.find((m) => m.name === value)
                  if (method) {
                    // Передаем весь объект метода доставки
                    handleMethodSelect(method)
                  }
                } else {
                  // Если индекс невалидный, показываем предупреждение
                  // console.log('Невозможно выбрать метод доставки: индекс отсутствует или невалиден')
                }
              }}
              classNames={{
                base: 'gap-3',
                wrapper: 'flex flex flex-row flex-wrap lg:flex-nowrap gap-3'
              }}
              errorMessage={error?.message}
            >
              {methodsWithPrices.map((method) => (
                <CustomRadio key={method.name} value={method.name}>
                  <div className='flex items-center gap-3'>
                    <div className='h-8 w-8 text-blue-600 [&>svg]:h-full [&>svg]:w-full' dangerouslySetInnerHTML={{ __html: method.icon }} />
                    <div>
                      <div className='font-semibold'>{method.title}</div>
                      <div className='text-sm'>
                        <div className='te'>{method.note}</div>
                        {shippingQuery.isFetching || shippingQuery.isRefetching ? (
                          <div className='mt-2 font-bold text-default-600'>
                            <span className='animate-pulse rounded-lg bg-default-50 px-2 text-xs text-default-600'>Расчет стоимости...</span>
                          </div>
                        ) : (
                          <div className='mt-2'>
                            {method.isFree && method.originalPrice && method.originalPrice > 0 ? (
                              <div className='flex items-center gap-2'>
                                <span className='text-sm text-default-500 line-through'>
                                  {priceFormat(Number(method.originalPrice) + Number(method.originalPrice) * 0.5)}
                                </span>
                                <span className='font-bold text-success-600'>Бесплатно</span>
                              </div>
                            ) : (
                              <div className='font-bold'>{method?.price !== undefined && priceFormat(method.price)}</div>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </CustomRadio>
              ))}
            </RadioGroup>
          )}
        />
        <div className='mt-4 flex w-full justify-end'>
          {showNextBtn && (
            <Button color='warning' variant='flat' type='submit' isDisabled={!shippingParams.destinationIndex || !isValidZipCode || !watch('shippingMethod')}>
              Продолжить
            </Button>
          )}
        </div>
      </Form>
      {(!shippingParams.destinationIndex || !isValidZipCode) && (
        <Alert className='mt-3' variant='flat' color='warning'>
          <span className='text-base'>
            {!shippingParams.destinationIndex
              ? 'Для расчета стоимости доставки необходимо указать почтовый индекс'
              : 'Указан некорректный индекс. Введите 6 цифр для расчета доставки'}
          </span>
        </Alert>
      )}
      <Alert className='mt-3' variant='flat' color='default'>
        <span className='text-base'>После отправки посылки вам будет направлен трек-номер для отслеживания её перемещения</span>
      </Alert>
    </>
  )
}

export const ShippingMethods = (
  props: ShippingMethodsProps & {
    shippingParams: {
      destinationIndex?: string
      isValidZipCode?: boolean
    }
  }
) => {
  return (
    <TrpcReactWrapper>
      <ShippingMethodsCalculator {...props} shippingParams={props.shippingParams} />
    </TrpcReactWrapper>
  )
}
