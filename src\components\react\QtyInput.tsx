import { getCartItem, updateCartItem } from '@/stores/cart'
import type { CategoryProduct } from '@/types/GetCategoryProduct'
import { Button, Input, useDisclosure } from '@heroui/react'
import { MinusIcon, PlusIcon, ShoppingCartIcon, Trash2Icon } from 'lucide-react'
import { useCallback, useEffect, useRef, useMemo, useReducer, type ReactNode, useState } from 'react'
import { memo } from 'react'
import { navigate } from 'astro:transitions/client'
// import { trpcReact } from '@/trpc'
import { trpc } from '@/trpc'
import { DeleteConfirmModal } from './DeleteConfirmModal'

// Импортируем компоненты для предзаказа
import { PreorderButton } from './PreorderButton'

interface Props {
  initialValue?: number
  product: Partial<CategoryProduct> & { prod_id: number; inStock?: boolean; prod_count?: number }
  isLoadingState?: boolean
  label?: string | ReactNode
  mini?: boolean
  showRemoveBtn?: boolean
  size?: 'sm' | 'md' | 'lg'
  cartMode?: boolean
  onAdded?: (product: Partial<CategoryProduct>) => void
  debounceDelay?: number
  // Пропс для выбора режима отображения предзаказа:
  // 'modal' — кнопка + модальное окно (открывается по клику, режим по умолчанию),
  // 'button' — используется компонент PreorderButton, который сам управляет отображением модалки.
  preorderMode?: 'modal' | 'button'
}

// Выносим все подкомпоненты наружу и мемоизируем их
const AddToCartButton = memo(
  ({
    mini = false,
    size = 'md',
    isLoading = false,
    onAdd,
    label = 'Добавить в корзину'
  }: {
    mini?: boolean
    size?: 'sm' | 'md' | 'lg'
    isLoading?: boolean
    onAdd: () => void
    label?: string | ReactNode
  }) => (
    <Button variant='flat' size={size} color='warning' isLoading={isLoading} onPress={onAdd}>
      <div className='flex items-center gap-3 font-bold'>
        {!mini && <span>{label}</span>}
        {!isLoading && <ShoppingCartIcon />}
      </div>
    </Button>
  )
)

const RemoveButton = memo(
  ({
    size = 'md',
    isLoading = false,
    isOpen = false,
    onOpen,
    onClose,
    onConfirm
  }: {
    size?: 'sm' | 'md' | 'lg'
    isLoading?: boolean
    isOpen?: boolean
    onOpen: () => void
    onClose: () => void
    onConfirm: () => void
  }) => (
    <>
      <Button isIconOnly className='rounded-r-none' variant='flat' size={size} color={isLoading ? 'default' : 'danger'} isLoading={isLoading} onPress={onOpen}>
        <Trash2Icon />
      </Button>
      <DeleteConfirmModal isOpen={isOpen} onClose={onClose} onConfirm={onConfirm} />
    </>
  )
)

const GoToCartButton = memo(({ size, isLoading }: { size: 'sm' | 'md' | 'lg'; isLoading: boolean }) => (
  <Button variant='flat' size={size} color='success' isLoading={isLoading} className='w-24 rounded-r-none' onPress={() => navigate('/cart')}>
    {!isLoading && (
      <div>
        <div className='font-bold'>В корзине</div>
        <div>перейти</div>
      </div>
    )}
  </Button>
))

const QuantityCounter = memo(
  ({
    qty,
    size = 'md',
    showRemoveBtn = false,
    mini = false,
    onIncrement,
    onDecrement,
    productCount,
    onRemove,
    onQtyChange // добавляем новый проп
  }: {
    qty: number
    size?: 'sm' | 'md' | 'lg'
    showRemoveBtn?: boolean
    mini?: boolean
    onIncrement: () => void
    onDecrement: () => void
    productCount?: number
    onRemove: () => void
    onQtyChange: (value: number) => void
  }) => {
    const [inputValue, setInputValue] = useState(qty.toString());

    useEffect(() => {
      setInputValue(qty.toString());
    }, [qty]);

    const handleChange = useCallback(
      (e: React.ChangeEvent<HTMLInputElement>) => {
        const val = e.target.value;
        // Разрешаем ввод пустого значения или 0, но не вызываем onRemove сразу
        setInputValue(val);
        const newValue = Number(val);
        if (!Number.isNaN(newValue) && newValue > 0) {
          const clampedValue = Math.min(Math.max(newValue, 1), productCount || Number.POSITIVE_INFINITY);
          onQtyChange(clampedValue);
        }
      },
      [productCount, onQtyChange]
    );

    const handleBlur = useCallback(() => {
      const newValue = Number(inputValue);
      if (inputValue === '' || newValue === 0) {
        onRemove();
      } else if (!Number.isNaN(newValue)) {
        const clampedValue = Math.min(Math.max(newValue, 1), productCount || Number.POSITIVE_INFINITY);
        if (clampedValue !== qty) {
          onQtyChange(clampedValue);
        }
        setInputValue(clampedValue.toString());
      } else {
        setInputValue(qty.toString());
      }
    }, [inputValue, onRemove, onQtyChange, productCount, qty]);

    const classNames = useMemo(
      () => ({
        input: 'w-12 text-center text-base rounded-none text-default-600 font-semibold [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none',
        inputWrapper: `p-0 ${showRemoveBtn || !mini ? ' rounded-l-none' : ''}`
      }),
      [showRemoveBtn, mini]
    )

    const startContent = useMemo(
      () => (
        <Button onPress={onDecrement} color='danger' isIconOnly className='w-6 min-w-0 rounded p-0' size='sm' variant='light'>
          <MinusIcon />
        </Button>
      ),
      [onDecrement]
    )

    const endContent = useMemo(
      () => (
        <Button onPress={onIncrement} color='success' isIconOnly className='w-6 min-w-0 rounded p-0' variant='light' size='sm'>
          <PlusIcon />
        </Button>
      ),
      [onIncrement]
    )

    return (
      <Input
        value={inputValue}
        size={size}
        onChange={handleChange}
        onBlur={handleBlur}
        type="number"
        min="1"
        max={productCount || undefined}
        classNames={classNames}
        variant='flat'
        startContent={startContent}
        endContent={endContent}
      />
    )
  }
)

// Кастомный хук для дебаунса функций
const useDebounceCallback = <T extends (arg: number) => void>(callback: T, delay: number): T => {
  const callbackRef = useRef(callback);

  useEffect(() => {
    callbackRef.current = callback;
  }, [callback]);

  return useMemo(() => {
    let timeoutId: NodeJS.Timeout | null = null;

    function debouncedCallback(...args: [number]) {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }

      timeoutId = setTimeout(() => {
        callbackRef.current(...args);
        timeoutId = null;
      }, delay);
    }

    return debouncedCallback as T;
  }, [delay]);
}

// Типы для редьюсера
type QtyState = {
  qty: number;
  localQty: number;
  isLoading: boolean;
  isInitialized: boolean;
};

type QtyAction =
  | { type: 'INITIALIZE'; payload: { qty: number } }
  | { type: 'SET_LOCAL_QTY'; payload: { qty: number } }
  | { type: 'SET_QTY'; payload: { qty: number } }
  | { type: 'SET_LOADING'; payload: { isLoading: boolean } };

// Редьюсер для управления состоянием
const qtyReducer = (state: QtyState, action: QtyAction): QtyState => {
  switch (action.type) {
    case 'INITIALIZE':
      return {
        ...state,
        qty: action.payload.qty,
        localQty: action.payload.qty,
        isInitialized: true
      };
    case 'SET_LOCAL_QTY':
      return {
        ...state,
        localQty: action.payload.qty
      };
    case 'SET_QTY':
      return {
        ...state,
        qty: action.payload.qty,
        localQty: action.payload.qty
      };
    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.payload.isLoading
      };
    default:
      return state;
  }
};

export const QtyInput = memo(({ product, initialValue = 0, onAdded, debounceDelay = 800, preorderMode = 'button', ...props }: Props) => {
  const [state, dispatch] = useReducer(qtyReducer, {
    qty: initialValue || 0,
    localQty: initialValue || 0,
    isLoading: props.isLoadingState || false,
    isInitialized: false
  });

  const { qty, localQty, isLoading, isInitialized } = state;

  // Рефы для отслеживания предыдущих значений
  const prevQtyRef = useRef(qty);
  const updateInProgressRef = useRef(false);


  // Хуки для модального окна и дебаунса
  const { isOpen, onOpen, onClose } = useDisclosure()

  // Используем кастомный хук для дебаунса
  const debouncedUpdateCart = useDebounceCallback((newQty: number) => {
    if (updateInProgressRef.current) return;
    if (newQty === prevQtyRef.current) return;

    updateCart(newQty);
  }, debounceDelay);



  // Инициализация компонента
  useEffect(() => {
    if (!isInitialized) {
      let initialQty = initialValue;

      // Если initialValue не установлен, проверяем корзину
      if (initialQty === 0) {
        const cartItem = getCartItem(product.prod_id);
        if (cartItem?.qty) {
          initialQty = cartItem.qty;
        }
      }

      dispatch({ type: 'INITIALIZE', payload: { qty: initialQty } });
      prevQtyRef.current = initialQty;
    }
  }, [isInitialized, initialValue, product.prod_id]);

  // Обновляем предыдущее значение при изменении qty
  useEffect(() => {
    prevQtyRef.current = qty;
  }, [qty]);

  // Отслеживаем изменения localQty и вызываем дебаунсированное обновление корзины
  useEffect(() => {
    if (isInitialized && localQty !== qty) {
      debouncedUpdateCart(localQty);
    }
  }, [localQty, qty, isInitialized, debouncedUpdateCart]);

  // Функция обновления корзины
  const updateCart = useCallback(async (newQty: number) => {
    // Предотвращаем лишние запросы
    if (newQty === qty) return;
    if (updateInProgressRef.current) return;

    updateInProgressRef.current = true;
    dispatch({ type: 'SET_LOADING', payload: { isLoading: true } });

    try {
      const maxQty = product.prod_count || Number.POSITIVE_INFINITY;
      const clampedQty = Math.min(newQty, maxQty);

      // Обновляем корзину в API
      await trpc.products.pushToCart.mutate({
        prodId: Number(product.prod_id),
        qty: clampedQty
      });

      // Обновляем локальное хранилище
      updateCartItem({ prod_id: Number(product.prod_id), qty: clampedQty });

      // Обновляем состояние
      dispatch({ type: 'SET_QTY', payload: { qty: clampedQty } });

      // Вызываем коллбэк при добавлении в корзину
      if (qty === 0 && clampedQty === 1) {
        onAdded?.(product);
      }
    } catch (error) {
      console.error('Failed to update cart:', error);
      dispatch({ type: 'SET_LOCAL_QTY', payload: { qty } });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: { isLoading: false } });
      updateInProgressRef.current = false;
    }
  }, [product, qty, onAdded]);

  // Обработчики событий
  const handleIncrement = useCallback(() => {
    const newQty = Math.min(localQty + 1, product.prod_count || Number.POSITIVE_INFINITY);
    dispatch({ type: 'SET_LOCAL_QTY', payload: { qty: newQty } });
  }, [localQty, product.prod_count]);

  const handleDecrement = useCallback(() => {
    if (localQty === 1) {
      onOpen();
    } else {
      const newQty = Math.max(localQty - 1, 1);
      dispatch({ type: 'SET_LOCAL_QTY', payload: { qty: newQty } });
    }
  }, [localQty, onOpen]);

  const handleRemove = useCallback(() => {
    dispatch({ type: 'SET_LOCAL_QTY', payload: { qty: 0 } });
    updateCart(0);
    onClose();
  }, [onClose, updateCart]);

  const handleAdd = useCallback(() => {
    updateCart(1);
  }, [updateCart]);

  const handleQtyChange = useCallback((newQty: number) => {
    dispatch({ type: 'SET_LOCAL_QTY', payload: { qty: newQty } });
  }, []);

  return (
    <>
      {localQty === 0 ? (
        // Если товара нет на складе, показываем кнопку предзаказа
        product.prod_count === 0 ? (
          <PreorderButton
            mini={props.mini}
            size={props.size}
            product={product}
            label='Оформить предзаказ'
            isTabler={false}
          />
        ) : (
          <AddToCartButton mini={props.mini} size={props.size} isLoading={isLoading} label={props.label || 'В корзину'} onAdd={handleAdd} />
        )
      ) : (
        <div className="flex items-center">
          {(props.cartMode || !props.mini) &&
            (props.cartMode || props.showRemoveBtn ? (
              <RemoveButton
                size={props.size || 'md'}
                isLoading={isLoading}
                isOpen={isOpen}
                onOpen={onOpen}
                onClose={onClose}
                onConfirm={handleRemove}
              />
            ) : (
              <GoToCartButton
                size={props.size || 'md'}
                isLoading={isLoading}
              />
            ))}
          <QuantityCounter
            qty={localQty}
            size={props.size}
            showRemoveBtn={props.showRemoveBtn}
            mini={props.mini}
            onIncrement={handleIncrement}
            onDecrement={handleDecrement}
            productCount={product.prod_count}
            onRemove={handleRemove}
            onQtyChange={handleQtyChange}
          />
          <DeleteConfirmModal isOpen={isOpen} onClose={onClose} onConfirm={handleRemove} />
        </div>
      )}
    </>
  )
})
