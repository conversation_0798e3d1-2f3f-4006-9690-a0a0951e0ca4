import { memo } from 'react'
import { Button } from '@heroui/react'
import { KeyIcon } from 'lucide-react'
import { openProductEditor } from '@/stores/productEditor'

interface ProductEditButtonProps {
  productId: string | number
  size?: 'sm' | 'md' | 'lg'
}

export const ProductEditButton = memo(({ productId, size = 'md' }: ProductEditButtonProps) => {
  return (
    <Button
      color="default"
      variant="flat"
      size={size}
      startContent={<KeyIcon className="h-4 w-4" />}
      onClick={() => openProductEditor(productId.toString())}
    >
      Редактировать
    </Button>
  )
})

ProductEditButton.displayName = 'ProductEditButton'
