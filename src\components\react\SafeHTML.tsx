import { useRef, useEffect } from 'react'

interface SafeHTMLProps {
  html: string
  className?: string
  maxLines?: number
}

export const SafeHTML = ({ html, className = '', }: SafeHTMLProps) => {
  const containerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (containerRef.current) {
      containerRef.current.innerHTML = html || ''
    }
  }, [html])

  return (
    <div 
      ref={containerRef}
      className={`overflow-hidden text-ellipsis line-clamp-3 ${className}`}
    />
  )
}
