import { Switch } from '@heroui/react'
import { useStore } from '@nanostores/react'
import { useState, useEffect } from 'react'
import { userStore } from '@/stores/user'
import { updateURLParams } from '@/stores/qs'

interface InStockFilterProps {
  isMobile?: boolean
  initialValue?: boolean
}

export const InStockFilter = ({ isMobile = false, initialValue = false }: InStockFilterProps) => {
  const user = useStore(userStore)
  const [isInStock, setIsInStock] = useState(initialValue)

  // Синхронизируем локальное состояние с initialValue
  useEffect(() => {
    setIsInStock(initialValue)
  }, [initialValue])

  // Не показываем фильтр неавторизованным пользователям
  if (!user?.client_id) {
    return null
  }

  const handleChange = (value: boolean) => {
    setIsInStock(value)
    
    // Обновляем URL параметры
    updateURLParams({ filters: { ['inStock']: value ? ['true'] : null } } as any)
  }

  if (isMobile) {
    return (
      <div className="flex items-center gap-2 px-4 py-2 border rounded-lg bg-white dark:bg-default-50">
        <Switch
          size="sm"
          color="primary"
          isSelected={isInStock}
          onValueChange={handleChange}
        />
        <span className="text-sm font-medium whitespace-nowrap">В наличии</span>
      </div>
    )
  }

  return (
    <div className="">
      {/* <div className="font-bold text-sm text-default-900">Наличие</div> */}
      <div className="flex items-center gap-2 border dark:border-default-200 py-2 px-2 rounded-md">
        <Switch
          size="sm" 
          color="warning"
          isSelected={isInStock}
          onValueChange={handleChange}
        >
          Только в наличии
          </Switch>
      </div>
    </div>
  )
} 