import { useEffect, useState } from 'react'
import { navigate } from 'astro:transitions/client'
import { Spinner } from '@heroui/react'

interface YooKassaProps {
  confirmation_token: string
  orderId: string
  amount: string
  orderNumber: string
  isLoading?: boolean
  onLoadingChange?: (loading: boolean) => void
}

export const YooKassa = ({
  confirmation_token,
  orderId,
  onLoadingChange
}: YooKassaProps) => {
  const [loading, setLoading] = useState(true)

  // Обновляем состояние загрузки и вызываем колбэк, если он предоставлен
  const updateLoading = (state: boolean) => {
    setLoading(state)
    onLoadingChange?.(state)
  }

  useEffect(() => {
    // Если нет токена, не продолжаем
    if (!confirmation_token) return;
    // Функция для инициализации виджета YooKassa
    const initYooKassa = () => {
      if (!confirmation_token) {
        console.error('confirmation_token не найден')
        return
      }

      if (typeof window === 'undefined') return

      if (!window.YooMoneyCheckoutWidget) {
        console.error('YooMoneyCheckoutWidget не загружен')
        return
      }

      // Инициализация виджета
      const checkout = new window.YooMoneyCheckoutWidget({
        confirmation_token, // Токен, полученный от ЮKassa
        error_callback: (error: unknown) => {
          console.error('Ошибка YooKassa:', error)
        }
      })

      // Обновляем состояние загрузки
      updateLoading(false)

      // Обработчик успешной оплаты
      checkout.on('complete', () => {
        // Отправляем запрос на сервер для обновления статуса заказа
        fetch(`/api/service/yookassa/successpayment?orderId=${orderId}`, {
          method: 'GET',
          credentials: 'include'
        })
        navigate(`/successpayment/${orderId}`)
        checkout.destroy()
      })

      // Отображаем платежную форму в контейнере
      checkout.render('payment-form')
    }

    // Проверяем, загружен ли скрипт YooKassa
    if (window.YooMoneyCheckoutWidget) {
      initYooKassa()
    } else {
      // Если скрипт еще не загружен, добавляем его на страницу
      const script = document.createElement('script')
      script.src = 'https://yookassa.ru/checkout-widget/v1/checkout-widget.js'
      script.async = true
      script.onload = initYooKassa
      document.head.appendChild(script)

      // Очистка при размонтировании компонента
      return () => {
        document.head.removeChild(script)
      }
    }
  }, [confirmation_token, orderId])

  return (
    <div>
      {loading && (
        <div className="flex items-center justify-center gap-4 my-4">
          <Spinner color="warning" /> Загрузка платежной формы...
        </div>
      )}
      <div id="payment-form" />

      {/* Тестовые данные для разработки */}
      {/* {process.env.NODE_ENV === 'development' && (
        <div className="mt-4 p-4 bg-gray-100 rounded-lg text-sm">
          <p>Данные банковской карты для оплаты в <b>тестовом режиме</b>:</p>
          <ul className="list-disc pl-5 mt-2">
            <li>номер — <b>5555 5555 5555 4477</b></li>
            <li>срок действия — <b>01/30</b> (или другая дата, больше текущей)</li>
            <li>CVC — <b>123</b> (или три любые цифры)</li>
            <li>код для прохождения 3-D Secure — <b>123</b> (или три любые цифры)</li>
          </ul>
        </div>
      )} */}
    </div>
  )
}

// Добавляем типы для глобального объекта window
declare global {
  interface Window {
    YooMoneyCheckoutWidget: {
      new(options: {
        confirmation_token: string;
        error_callback?: (error: unknown) => void;
      }): {
        on(event: string, callback: () => void): void;
        render(containerId: string): void;
        destroy(): void;
      }
    }
  }
}

export default YooKassa
