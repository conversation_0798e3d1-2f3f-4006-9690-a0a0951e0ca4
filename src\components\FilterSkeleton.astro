---
interface Props {
  title?: string
}

const { title } = Astro.props
---

<div>
  <div class=''>
    <div class='mb-2 flex items-center justify-between'>
      <h2 class='text-lg font-semibold'>{title || ''}</h2>
      <span class='text-sm text-default-600'></span>
    </div>

    <div class='mb-3'>
      <div class='flex items-center rounded-lg bg-default-100 p-2'>
        <svg class='h-5 w-5 text-default-500' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
          <path stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z'></path>
        </svg>
        <div class='ml-2 h-5 w-full animate-pulse rounded bg-default-200'></div>
      </div>
    </div>

    <div class='space-y-5'>
      <div class='flex items-center'>
        <div class='h-4 w-4 animate-pulse rounded bg-default-200'></div>
        <div class='ml-3 h-4 w-16 animate-pulse rounded bg-default-200'></div>
      </div>
      <div class='flex items-center'>
        <div class='h-4 w-4 animate-pulse rounded bg-default-200'></div>
        <div class='ml-3 h-4 w-20 animate-pulse rounded bg-default-200'></div>
      </div>
      <div class='flex items-center'>
        <div class='h-4 w-4 animate-pulse rounded bg-default-200'></div>
        <div class='ml-3 h-4 w-20 animate-pulse rounded bg-default-200'></div>
      </div>
      <div class='flex items-center'>
        <div class='h-4 w-4 animate-pulse rounded bg-default-200'></div>
        <div class='ml-3 h-4 w-20 animate-pulse rounded bg-default-200'></div>
      </div>
      <div class='flex items-center'>
        <div class='h-4 w-4 animate-pulse rounded bg-default-200'></div>
        <div class='ml-3 h-4 w-14 animate-pulse rounded bg-default-200'></div>
      </div>
      <div class='flex items-center'>
        <div class='h-4 w-4 animate-pulse rounded bg-default-200'></div>
        <div class='w-18 ml-3 h-4 animate-pulse rounded bg-default-200'></div>
      </div>
    </div>
  </div>
</div>
